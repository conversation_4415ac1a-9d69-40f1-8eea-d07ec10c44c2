"""
Middleware for rate limiting, caching, and request processing
"""

import time
import json
import hashlib
from typing import Dict, Any, Optional
from fastapi import Request, Response, HTTPException, status
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)

class InMemoryCache:
    """Simple in-memory cache for development (use Redis in production)"""
    
    def __init__(self, default_ttl: int = 300):  # 5 minutes default
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        if key in self.cache:
            entry = self.cache[key]
            if time.time() < entry['expires']:
                return entry['value']
            else:
                # Expired, remove from cache
                del self.cache[key]
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with TTL"""
        if ttl is None:
            ttl = self.default_ttl
        
        self.cache[key] = {
            'value': value,
            'expires': time.time() + ttl
        }
    
    def delete(self, key: str) -> None:
        """Delete key from cache"""
        if key in self.cache:
            del self.cache[key]
    
    def clear(self) -> None:
        """Clear all cache"""
        self.cache.clear()
    
    def cleanup_expired(self) -> None:
        """Remove expired entries"""
        current_time = time.time()
        expired_keys = [
            key for key, entry in self.cache.items()
            if current_time >= entry['expires']
        ]
        for key in expired_keys:
            del self.cache[key]

class RateLimitMiddleware:
    """Rate limiting middleware"""
    
    def __init__(self, requests_per_minute: int = 60):
        self.requests_per_minute = requests_per_minute
        self.requests: Dict[str, list] = {}
    
    def is_allowed(self, client_ip: str) -> bool:
        """Check if request is allowed"""
        current_time = time.time()
        minute_ago = current_time - 60
        
        if client_ip not in self.requests:
            self.requests[client_ip] = []
        
        # Remove requests older than 1 minute
        self.requests[client_ip] = [
            req_time for req_time in self.requests[client_ip]
            if req_time > minute_ago
        ]
        
        # Check if under limit
        if len(self.requests[client_ip]) < self.requests_per_minute:
            self.requests[client_ip].append(current_time)
            return True
        
        return False
    
    def get_reset_time(self, client_ip: str) -> int:
        """Get time until rate limit resets"""
        if client_ip not in self.requests or not self.requests[client_ip]:
            return 0
        
        oldest_request = min(self.requests[client_ip])
        reset_time = oldest_request + 60
        return max(0, int(reset_time - time.time()))

# Global instances
cache = InMemoryCache()
rate_limiter = RateLimitMiddleware()

def get_cache_key(prefix: str, **kwargs) -> str:
    """Generate cache key from parameters"""
    # Create a deterministic key from the parameters
    key_data = json.dumps(kwargs, sort_keys=True)
    key_hash = hashlib.md5(key_data.encode()).hexdigest()
    return f"{prefix}:{key_hash}"

async def rate_limit_middleware(request: Request, call_next):
    """Rate limiting middleware function"""
    client_ip = request.client.host
    
    # Skip rate limiting for health checks
    if request.url.path in ["/", "/api/health"]:
        response = await call_next(request)
        return response
    
    if not rate_limiter.is_allowed(client_ip):
        reset_time = rate_limiter.get_reset_time(client_ip)
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "detail": "Rate limit exceeded",
                "retry_after": reset_time
            },
            headers={
                "Retry-After": str(reset_time),
                "X-RateLimit-Limit": str(rate_limiter.requests_per_minute),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": str(int(time.time()) + reset_time)
            }
        )
    
    response = await call_next(request)
    
    # Add rate limit headers
    remaining = rate_limiter.requests_per_minute - len(rate_limiter.requests.get(client_ip, []))
    response.headers["X-RateLimit-Limit"] = str(rate_limiter.requests_per_minute)
    response.headers["X-RateLimit-Remaining"] = str(max(0, remaining))
    
    return response

class CacheService:
    """Service for caching translations and search results"""
    
    @staticmethod
    def get_translation_cache_key(text: str, source_lang: str, target_lang: str) -> str:
        """Get cache key for translation"""
        return get_cache_key("translation", text=text, source=source_lang, target=target_lang)
    
    @staticmethod
    def get_search_cache_key(query: str, limit: int) -> str:
        """Get cache key for search results"""
        return get_cache_key("search", query=query, limit=limit)
    
    @staticmethod
    def cache_translation(text: str, source_lang: str, target_lang: str, result: Dict[str, Any], ttl: int = 3600) -> None:
        """Cache translation result"""
        key = CacheService.get_translation_cache_key(text, source_lang, target_lang)
        cache.set(key, result, ttl)
        logger.debug(f"Cached translation: {key}")
    
    @staticmethod
    def get_cached_translation(text: str, source_lang: str, target_lang: str) -> Optional[Dict[str, Any]]:
        """Get cached translation result"""
        key = CacheService.get_translation_cache_key(text, source_lang, target_lang)
        result = cache.get(key)
        if result:
            logger.debug(f"Cache hit for translation: {key}")
        return result
    
    @staticmethod
    def cache_search_results(query: str, limit: int, results: Dict[str, Any], ttl: int = 1800) -> None:
        """Cache search results"""
        key = CacheService.get_search_cache_key(query, limit)
        cache.set(key, results, ttl)
        logger.debug(f"Cached search results: {key}")
    
    @staticmethod
    def get_cached_search_results(query: str, limit: int) -> Optional[Dict[str, Any]]:
        """Get cached search results"""
        key = CacheService.get_search_cache_key(query, limit)
        result = cache.get(key)
        if result:
            logger.debug(f"Cache hit for search: {key}")
        return result

# Cleanup task for cache
async def cleanup_cache_task():
    """Periodic task to cleanup expired cache entries"""
    while True:
        try:
            cache.cleanup_expired()
            await asyncio.sleep(300)  # Run every 5 minutes
        except Exception as e:
            logger.error(f"Cache cleanup error: {e}")
            await asyncio.sleep(60)  # Retry after 1 minute on error
