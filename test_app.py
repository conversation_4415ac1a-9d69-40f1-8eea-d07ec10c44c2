#!/usr/bin/env python3
"""
Test script for the Douyin Translator App
This script tests all the main functionality of the application.
"""

import requests
import json
import time

API_BASE = "http://localhost:8000"

def test_health():
    """Test the health endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{API_BASE}/api/health")
        if response.status_code == 200:
            print("✅ Health check passed:", response.json())
            return True
        else:
            print("❌ Health check failed:", response.status_code)
            return False
    except Exception as e:
        print("❌ Health check error:", e)
        return False

def test_translation():
    """Test the translation endpoint"""
    print("\n🌐 Testing translation endpoint...")
    
    test_cases = [
        {"text": "Hello world", "source_language": "en"},
        {"text": "Good morning", "source_language": "auto"},
        {"text": "Thank you", "source_language": "en"},
        {"text": "How are you", "source_language": "auto"}
    ]
    
    for test_case in test_cases:
        try:
            response = requests.post(
                f"{API_BASE}/api/translate",
                headers={"Content-Type": "application/json"},
                json=test_case
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ '{test_case['text']}' → '{result['translated_text']}'")
            else:
                print(f"❌ Translation failed for '{test_case['text']}': {response.status_code}")
                
        except Exception as e:
            print(f"❌ Translation error for '{test_case['text']}': {e}")

def test_search():
    """Test the video search endpoint"""
    print("\n🎥 Testing video search endpoint...")
    
    search_queries = [
        {"query": "你好世界", "limit": 3},
        {"query": "早上好", "limit": 5},
        {"query": "测试视频", "limit": 2}
    ]
    
    for query in search_queries:
        try:
            response = requests.post(
                f"{API_BASE}/api/search",
                headers={"Content-Type": "application/json"},
                json=query
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Found {len(result['videos'])} videos for '{query['query']}'")
                for i, video in enumerate(result['videos'][:2], 1):
                    print(f"   {i}. {video['title']} by {video['author']} ({video['duration']})")
            else:
                print(f"❌ Search failed for '{query['query']}': {response.status_code}")
                
        except Exception as e:
            print(f"❌ Search error for '{query['query']}': {e}")

def test_full_workflow():
    """Test the complete workflow: translate → search"""
    print("\n🔄 Testing complete workflow...")
    
    # Step 1: Translate English to Chinese
    english_text = "Hello world"
    print(f"1. Translating: '{english_text}'")
    
    try:
        translate_response = requests.post(
            f"{API_BASE}/api/translate",
            headers={"Content-Type": "application/json"},
            json={"text": english_text, "source_language": "en"}
        )
        
        if translate_response.status_code == 200:
            translation_result = translate_response.json()
            chinese_text = translation_result['translated_text']
            print(f"   ✅ Translation: '{chinese_text}'")
            
            # Step 2: Search for videos using the Chinese text
            print(f"2. Searching videos for: '{chinese_text}'")
            
            search_response = requests.post(
                f"{API_BASE}/api/search",
                headers={"Content-Type": "application/json"},
                json={"query": chinese_text, "limit": 3}
            )
            
            if search_response.status_code == 200:
                search_result = search_response.json()
                print(f"   ✅ Found {len(search_result['videos'])} videos")
                
                for i, video in enumerate(search_result['videos'], 1):
                    print(f"   {i}. {video['title']}")
                    print(f"      Author: {video['author']}, Duration: {video['duration']}")
                    print(f"      URL: {video['url']}")
                
                print("✅ Complete workflow successful!")
                return True
            else:
                print(f"   ❌ Search failed: {search_response.status_code}")
                return False
        else:
            print(f"   ❌ Translation failed: {translate_response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Workflow error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Douyin Translator App Tests")
    print("=" * 50)
    
    # Test individual endpoints
    health_ok = test_health()
    if not health_ok:
        print("❌ Health check failed. Make sure the backend is running on port 8000.")
        return
    
    test_translation()
    test_search()
    
    # Test complete workflow
    workflow_ok = test_full_workflow()
    
    print("\n" + "=" * 50)
    if workflow_ok:
        print("🎉 All tests completed successfully!")
        print("\n📱 You can now open http://localhost:3000 in your browser to use the app!")
    else:
        print("⚠️  Some tests failed. Check the backend logs for more details.")

if __name__ == "__main__":
    main()
