#!/usr/bin/env python3
import asyncio
from googletrans import Translator

async def test_translation():
    translator = Translator()
    
    try:
        # Test basic translation
        result = translator.translate("Hello world", src='en', dest='zh')
        print(f"Translation successful: {result.text}")
        print(f"Source language: {result.src}")
        print(f"Destination language: {result.dest}")
        
        # Test with different Chinese codes
        for dest in ['zh', 'zh-cn', 'chinese']:
            try:
                result = translator.translate("Hello", src='en', dest=dest)
                print(f"Success with {dest}: {result.text}")
            except Exception as e:
                print(f"Failed with {dest}: {e}")
                
    except Exception as e:
        print(f"Translation failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_translation())
