"use client";

import { useState } from "react";
import { toast } from "react-hot-toast";
import {
  Download,
  ExternalLink,
  Clock,
  User,
  Loader2,
  CheckCircle,
} from "lucide-react";
import { downloadVideo } from "@/utils/api";
import { VideoInfo } from "@/types";

interface VideoResultsProps {
  videos: VideoInfo[];
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

export default function VideoResults({
  videos,
  isLoading,
  setIsLoading,
}: VideoResultsProps) {
  const [downloadingVideos, setDownloadingVideos] = useState<Set<string>>(
    new Set()
  );
  const [downloadedVideos, setDownloadedVideos] = useState<Set<string>>(
    new Set()
  );
  const [downloadQuality, setDownloadQuality] = useState<string>("best");
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false);

  const handleDownload = async (video: VideoInfo) => {
    if (!video.url) {
      toast.error("Video URL not available");
      return;
    }

    setDownloadingVideos((prev) => new Set(prev).add(video.url));

    try {
      const result = await downloadVideo({
        video_url: video.url,
        quality: downloadQuality,
      });

      if (result.success) {
        setDownloadedVideos((prev) => new Set(prev).add(video.url));
        toast.success(`Downloaded: ${video.title}`);

        if (result.file_path) {
          // Extract filename from path - handle both forward and backward slashes
          let filename = result.file_path;

          // Remove any directory path prefixes
          if (filename.includes("/")) {
            filename = filename.split("/").pop() || filename;
          }
          if (filename.includes("\\")) {
            filename = filename.split("\\").pop() || filename;
          }

          // Remove 'downloads/' prefix if it exists in the filename
          if (
            filename.startsWith("downloads/") ||
            filename.startsWith("downloads\\")
          ) {
            filename = filename.replace(/^downloads[\/\\]/, "");
          }

          console.log("Original path:", result.file_path);
          console.log("Extracted filename:", filename);

          if (filename) {
            // Create a download link for the user
            const link = document.createElement("a");
            link.href = `http://localhost:8000/api/download-file/${filename}`;
            link.download = filename;
            link.target = "_blank";
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Show additional success message with file info
            toast.success(`File ready: ${filename} (${result.file_size})`, {
              duration: 5000,
            });
          }
        }
      } else {
        toast.error(result.message || "Download failed");
      }
    } catch (error) {
      console.error("Download error:", error);
      toast.error(error instanceof Error ? error.message : "Download failed");
    } finally {
      setDownloadingVideos((prev) => {
        const newSet = new Set(prev);
        newSet.delete(video.url);
        return newSet;
      });
    }
  };

  const isVideoDownloading = (videoUrl: string) =>
    downloadingVideos.has(videoUrl);
  const isVideoDownloaded = (videoUrl: string) =>
    downloadedVideos.has(videoUrl);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 md:p-8">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <Download className="w-6 h-6 text-green-600 dark:text-green-400" />
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            Video Results
          </h2>
        </div>
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-sm text-blue-600 dark:text-blue-400 hover:underline"
          >
            {showAdvanced ? "Hide" : "Show"} Options
          </button>
          <span className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
            {videos.length} videos found
          </span>
        </div>
      </div>

      {showAdvanced && (
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Download Quality:
            </label>
            <select
              value={downloadQuality}
              onChange={(e) => setDownloadQuality(e.target.value)}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white text-sm"
            >
              <option value="best">Best Quality</option>
              <option value="high">High Quality</option>
              <option value="medium">Medium Quality</option>
              <option value="low">Low Quality</option>
            </select>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              Higher quality = larger file size
            </span>
          </div>
        </div>
      )}

      <div className="grid gap-4 md:gap-6">
        {videos.map((video, index) => (
          <div
            key={`${video.url}-${index}`}
            className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-200"
          >
            <div className="flex flex-col md:flex-row gap-4">
              {/* Video Thumbnail */}
              <div className="flex-shrink-0">
                {video.thumbnail ? (
                  <img
                    src={video.thumbnail}
                    alt={video.title}
                    className="w-full md:w-32 h-24 object-cover rounded-lg"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = "none";
                    }}
                  />
                ) : (
                  <div className="w-full md:w-32 h-24 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                    <Download className="w-8 h-8 text-gray-400" />
                  </div>
                )}
              </div>

              {/* Video Info */}
              <div className="flex-grow">
                <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-2 line-clamp-2">
                  {video.title}
                </h3>

                <div className="flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {video.author && (
                    <div className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      <span>{video.author}</span>
                    </div>
                  )}
                  {video.duration && (
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>{video.duration}</span>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => handleDownload(video)}
                    disabled={
                      isVideoDownloading(video.url) ||
                      isVideoDownloaded(video.url)
                    }
                    className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2 ${
                      isVideoDownloaded(video.url)
                        ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 cursor-not-allowed"
                        : isVideoDownloading(video.url)
                        ? "bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 cursor-not-allowed"
                        : "bg-green-600 hover:bg-green-700 text-white"
                    }`}
                  >
                    {isVideoDownloaded(video.url) ? (
                      <>
                        <CheckCircle className="w-4 h-4" />
                        Downloaded
                      </>
                    ) : isVideoDownloading(video.url) ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Downloading...
                      </>
                    ) : (
                      <>
                        <Download className="w-4 h-4" />
                        Download
                      </>
                    )}
                  </button>

                  {video.url && (
                    <a
                      href={video.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
                    >
                      <ExternalLink className="w-4 h-4" />
                      View Original
                    </a>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <p className="text-sm text-yellow-800 dark:text-yellow-200">
          ⚠️ <strong>Note:</strong> Video downloads may take some time depending
          on the video size and quality. Please be patient and ensure you have
          permission to download the content.
        </p>
      </div>
    </div>
  );
}
