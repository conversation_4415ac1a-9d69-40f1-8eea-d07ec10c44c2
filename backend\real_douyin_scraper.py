#!/usr/bin/env python3
"""
Real Douyin Scraper - Extract actual video URLs from Douyin
"""

import requests
import re
import json
import time
import random
import urllib.parse
from pathlib import Path
import hashlib
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import yt_dlp

class RealDouyinScraper:
    def __init__(self):
        self.downloads_dir = Path("downloads")
        self.downloads_dir.mkdir(exist_ok=True)
        
        # Real mobile user agents for Douyin
        self.mobile_agents = [
            'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1 MicroMessenger/8.0.20',
            'Mozilla/5.0 (Linux; Android 11; SAMSUNG SM-G973U) AppleWebKit/537.36 (KHTML, like Gecko) SamsungBrowser/14.2 Chrome/87.0.4280.141 Mobile Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        ]
        
        # Douyin API endpoints and patterns
        self.douyin_patterns = {
            'video_id': r'/video/(\d+)',
            'aweme_id': r'aweme_id["\']?\s*[:=]\s*["\']?(\d+)',
            'video_url': r'"play_url"[^}]*"url_list":\s*\[([^\]]+)\]',
            'download_url': r'"download_url"[^}]*"url_list":\s*\[([^\]]+)\]'
        }

    def extract_real_douyin_video(self, douyin_url):
        """Extract real video from Douyin URL using multiple methods"""
        try:
            print(f"🎥 Extracting real Douyin video: {douyin_url}")
            
            # Method 1: Try yt-dlp (most reliable)
            result = self._extract_with_ytdlp(douyin_url)
            if result['success']:
                return result
            
            # Method 2: Try direct API extraction
            result = self._extract_with_api(douyin_url)
            if result['success']:
                return result
            
            # Method 3: Try Selenium browser automation
            result = self._extract_with_selenium(douyin_url)
            if result['success']:
                return result
            
            # Method 4: Try manual parsing
            result = self._extract_with_parsing(douyin_url)
            if result['success']:
                return result
            
            return {
                'success': False,
                'message': 'All extraction methods failed',
                'video_url': None,
                'title': None
            }
            
        except Exception as e:
            print(f"❌ Extraction error: {e}")
            return {
                'success': False,
                'message': f'Extraction error: {str(e)}',
                'video_url': None,
                'title': None
            }

    def _extract_with_ytdlp(self, douyin_url):
        """Method 1: Use yt-dlp for extraction"""
        try:
            print("   Trying yt-dlp extraction...")
            
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': False,
                'format': 'best[ext=mp4]',
                'user_agent': random.choice(self.mobile_agents),
                'referer': 'https://www.douyin.com/',
                'headers': {
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Accept-Encoding': 'gzip, deflate, br',
                }
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                info = ydl.extract_info(douyin_url, download=False)
                
                if info and 'url' in info:
                    print(f"   ✅ yt-dlp found video URL")
                    return {
                        'success': True,
                        'video_url': info['url'],
                        'title': info.get('title', 'Douyin Video'),
                        'duration': info.get('duration'),
                        'thumbnail': info.get('thumbnail')
                    }
            
            return {'success': False, 'message': 'yt-dlp extraction failed'}
            
        except Exception as e:
            print(f"   ⚠️ yt-dlp failed: {e}")
            return {'success': False, 'message': f'yt-dlp error: {str(e)}'}

    def _extract_with_api(self, douyin_url):
        """Method 2: Direct API extraction"""
        try:
            print("   Trying direct API extraction...")
            
            # Extract video ID from URL
            video_id = self._extract_video_id(douyin_url)
            if not video_id:
                return {'success': False, 'message': 'Could not extract video ID'}
            
            # Try Douyin API endpoints
            api_urls = [
                f"https://www.douyin.com/aweme/v1/web/aweme/detail/?aweme_id={video_id}",
                f"https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids={video_id}",
            ]
            
            session = requests.Session()
            session.headers.update({
                'User-Agent': random.choice(self.mobile_agents),
                'Referer': 'https://www.douyin.com/',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            })
            
            for api_url in api_urls:
                try:
                    response = session.get(api_url, timeout=15)
                    
                    if response.status_code == 200:
                        data = response.json()
                        
                        # Parse video URL from response
                        video_url = self._parse_video_url_from_api(data)
                        if video_url:
                            print(f"   ✅ API extraction successful")
                            return {
                                'success': True,
                                'video_url': video_url,
                                'title': self._parse_title_from_api(data),
                                'api_data': data
                            }
                            
                except Exception as e:
                    print(f"   API {api_url} failed: {e}")
                    continue
            
            return {'success': False, 'message': 'All API endpoints failed'}
            
        except Exception as e:
            print(f"   ⚠️ API extraction failed: {e}")
            return {'success': False, 'message': f'API error: {str(e)}'}

    def _extract_with_selenium(self, douyin_url):
        """Method 3: Browser automation with Selenium"""
        try:
            print("   Trying Selenium browser automation...")
            
            # Setup Chrome options for mobile simulation
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=375,667')  # iPhone size
            chrome_options.add_argument(f'--user-agent={random.choice(self.mobile_agents)}')

            # Use webdriver-manager to automatically handle Chrome driver
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)
            
            try:
                # Load the page
                driver.get(douyin_url)
                
                # Wait for video element
                wait = WebDriverWait(driver, 10)
                video_element = wait.until(
                    EC.presence_of_element_located((By.TAG_NAME, "video"))
                )
                
                # Extract video source
                video_src = video_element.get_attribute('src')
                if video_src:
                    print(f"   ✅ Selenium found video source")
                    return {
                        'success': True,
                        'video_url': video_src,
                        'title': driver.title or 'Douyin Video'
                    }
                
                # Try to find video URLs in page source
                page_source = driver.page_source
                video_urls = self._extract_video_urls_from_html(page_source)
                
                if video_urls:
                    print(f"   ✅ Selenium found video URLs in source")
                    return {
                        'success': True,
                        'video_url': video_urls[0],
                        'title': driver.title or 'Douyin Video'
                    }
                
                return {'success': False, 'message': 'No video found with Selenium'}
                
            finally:
                driver.quit()
                
        except Exception as e:
            print(f"   ⚠️ Selenium failed: {e}")
            return {'success': False, 'message': f'Selenium error: {str(e)}'}

    def _extract_with_parsing(self, douyin_url):
        """Method 4: Manual HTML parsing"""
        try:
            print("   Trying manual HTML parsing...")
            
            session = requests.Session()
            session.headers.update({
                'User-Agent': random.choice(self.mobile_agents),
                'Referer': 'https://www.douyin.com/',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            })
            
            response = session.get(douyin_url, timeout=15)
            
            if response.status_code == 200:
                html_content = response.text
                
                # Extract video URLs using regex patterns
                video_urls = self._extract_video_urls_from_html(html_content)
                
                if video_urls:
                    print(f"   ✅ Manual parsing found {len(video_urls)} video URLs")
                    return {
                        'success': True,
                        'video_url': video_urls[0],
                        'title': self._extract_title_from_html(html_content)
                    }
            
            return {'success': False, 'message': 'Manual parsing found no videos'}
            
        except Exception as e:
            print(f"   ⚠️ Manual parsing failed: {e}")
            return {'success': False, 'message': f'Parsing error: {str(e)}'}

    def _extract_video_id(self, url):
        """Extract video ID from Douyin URL"""
        patterns = [
            r'/video/(\d+)',
            r'modal_id=(\d+)',
            r'aweme_id=(\d+)',
            r'/(\d+)/?$'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None

    def _extract_video_urls_from_html(self, html_content):
        """Extract video URLs from HTML content"""
        video_urls = []
        
        patterns = [
            r'"play_url"[^}]*"url_list":\s*\[([^\]]+)\]',
            r'"download_url"[^}]*"url_list":\s*\[([^\]]+)\]',
            r'"video"[^}]*"play_url"[^}]*"uri":"([^"]+)"',
            r'https?://[^"\']+\.mp4[^"\']*',
            r'https?://[^"\']+\.m3u8[^"\']*'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, html_content)
            for match in matches:
                if isinstance(match, str) and ('http' in match or 'mp4' in match):
                    # Clean up the URL
                    url = match.replace('\\"', '"').replace('\\/', '/')
                    if url.startswith('http'):
                        video_urls.append(url)
        
        return list(set(video_urls))  # Remove duplicates

    def _extract_title_from_html(self, html_content):
        """Extract title from HTML content"""
        patterns = [
            r'<title>([^<]+)</title>',
            r'"desc":"([^"]+)"',
            r'"title":"([^"]+)"'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, html_content)
            if match:
                return match.group(1).strip()
        
        return "Douyin Video"

    def _parse_video_url_from_api(self, api_data):
        """Parse video URL from API response"""
        try:
            # Common paths in Douyin API responses
            paths = [
                ['aweme_detail', 'video', 'play_url', 'url_list'],
                ['item_list', 0, 'video', 'play_url', 'url_list'],
                ['aweme_list', 0, 'video', 'play_url', 'url_list'],
                ['data', 'aweme_detail', 'video', 'play_url', 'url_list']
            ]
            
            for path in paths:
                try:
                    current = api_data
                    for key in path:
                        if isinstance(key, int):
                            current = current[key]
                        else:
                            current = current[key]
                    
                    if isinstance(current, list) and len(current) > 0:
                        return current[0]
                        
                except (KeyError, IndexError, TypeError):
                    continue
            
            return None
            
        except Exception as e:
            print(f"   API parsing error: {e}")
            return None

    def _parse_title_from_api(self, api_data):
        """Parse title from API response"""
        try:
            paths = [
                ['aweme_detail', 'desc'],
                ['item_list', 0, 'desc'],
                ['aweme_list', 0, 'desc'],
                ['data', 'aweme_detail', 'desc']
            ]
            
            for path in paths:
                try:
                    current = api_data
                    for key in path:
                        if isinstance(key, int):
                            current = current[key]
                        else:
                            current = current[key]
                    
                    if isinstance(current, str) and current.strip():
                        return current.strip()
                        
                except (KeyError, IndexError, TypeError):
                    continue
            
            return "Douyin Video"
            
        except Exception as e:
            return "Douyin Video"

    def download_real_video(self, video_url, filename):
        """Download the actual video file"""
        try:
            print(f"📥 Downloading real video: {filename}")
            
            headers = {
                'User-Agent': random.choice(self.mobile_agents),
                'Referer': 'https://www.douyin.com/',
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive'
            }
            
            response = requests.get(video_url, headers=headers, stream=True, timeout=30)
            
            if response.status_code == 200:
                filepath = self.downloads_dir / filename
                
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                file_size = filepath.stat().st_size
                
                if file_size > 1000:  # At least 1KB
                    print(f"   ✅ Real video downloaded: {file_size} bytes")
                    return {
                        'success': True,
                        'filepath': str(filepath),
                        'filename': filename,
                        'file_size': file_size
                    }
                else:
                    filepath.unlink()  # Delete small file
                    return {'success': False, 'message': 'Downloaded file too small'}
            
            return {'success': False, 'message': f'HTTP {response.status_code}'}
            
        except Exception as e:
            print(f"   ❌ Download error: {e}")
            return {'success': False, 'message': f'Download error: {str(e)}'}

# Test the real scraper
if __name__ == "__main__":
    scraper = RealDouyinScraper()
    
    # Test URLs (replace with real Douyin URLs)
    test_urls = [
        "https://www.douyin.com/video/7234567890123456789",  # Replace with real URL
        "https://v.douyin.com/ABC123/",  # Replace with real short URL
    ]
    
    for url in test_urls:
        print(f"\n🧪 Testing real extraction: {url}")
        result = scraper.extract_real_douyin_video(url)
        
        if result['success']:
            print(f"✅ Success: Found video URL")
            print(f"   Title: {result.get('title', 'N/A')}")
            print(f"   Video URL: {result['video_url'][:100]}...")
            
            # Try to download
            filename = f"real_douyin_{int(time.time())}.mp4"
            download_result = scraper.download_real_video(result['video_url'], filename)
            
            if download_result['success']:
                print(f"✅ Download successful: {download_result['filename']}")
            else:
                print(f"❌ Download failed: {download_result['message']}")
        else:
            print(f"❌ Failed: {result['message']}")
