#!/usr/bin/env python3
"""
Test Real Douyin Flow - Complete end-to-end test with real integration
"""

import requests
import time
import os
import json

def test_complete_real_douyin_flow():
    """Test the complete real Douyin integration flow"""
    
    print("🎯 TESTING COMPLETE REAL DOUYIN FLOW")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Step 1: Test Translation (should work as before)
    print("\n1️⃣ Testing Translation...")
    try:
        response = requests.post(
            f"{base_url}/api/translate",
            json={"text": "Buddha meditation wisdom", "source_language": "en"},
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            chinese_text = data['translated_text']
            print(f"   ✅ Translation successful!")
            print(f"   English: {data['original_text']}")
            print(f"   Chinese: {chinese_text}")
        else:
            print(f"   ❌ Translation failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Translation error: {e}")
        return False
    
    # Step 2: Test Real Douyin Search
    print(f"\n2️⃣ Testing Real Douyin Search...")
    try:
        response = requests.post(
            f"{base_url}/api/search",
            json={"query": chinese_text, "limit": 3},
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            videos = data['videos']
            print(f"   ✅ Real search successful!")
            print(f"   Query: {data['query']}")
            print(f"   Found: {len(videos)} videos")
            
            # Analyze the video URLs
            real_douyin_count = 0
            for i, video in enumerate(videos, 1):
                print(f"   {i}. {video['title']}")
                print(f"      URL: {video['url']}")
                print(f"      Author: {video.get('author', 'N/A')}")
                
                # Check if URL looks like real Douyin
                if 'douyin.com/video/' in video['url'] and len(video['url'].split('/')[-1]) > 10:
                    real_douyin_count += 1
                    print(f"      ✅ REAL Douyin URL pattern detected")
                else:
                    print(f"      ⚠️ Generated/fallback URL")
            
            print(f"   Real Douyin URLs: {real_douyin_count}/{len(videos)}")
            
            if not videos:
                print(f"   ❌ No videos found")
                return False
                
        else:
            print(f"   ❌ Search failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ Search error: {e}")
        return False
    
    # Step 3: Test Real Video Download
    print(f"\n3️⃣ Testing Real Video Download...")
    
    download_results = []
    
    for i, video in enumerate(videos[:2], 1):  # Test first 2 videos
        print(f"\n   📥 Download Test {i}: {video['title']}")
        print(f"      URL: {video['url']}")
        
        try:
            response = requests.post(
                f"{base_url}/api/download",
                json={"video_url": video['url'], "quality": "best"},
                timeout=45  # Longer timeout for real downloads
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data['success']:
                    file_path = data['file_path']
                    file_size = data['file_size']
                    message = data['message']
                    
                    print(f"      ✅ Download successful!")
                    print(f"      Message: {message}")
                    print(f"      File: {file_path}")
                    print(f"      Size: {file_size}")
                    
                    # Extract filename for verification
                    filename = file_path.split('/')[-1] if '/' in file_path else file_path.split('\\')[-1]
                    
                    # Check if file actually exists
                    full_path = f"backend/downloads/{filename}"
                    if os.path.exists(full_path):
                        actual_size = os.path.getsize(full_path)
                        print(f"      ✅ File verified: {actual_size} bytes")
                        
                        # Check file type
                        if filename.endswith('.mp4'):
                            print(f"      ✅ MP4 video file")
                        elif filename.endswith('.json'):
                            print(f"      ℹ️ JSON info file")
                        else:
                            print(f"      ℹ️ Other file type: {filename}")
                        
                        # Check for metadata
                        metadata_path = full_path.replace('.mp4', '.json')
                        if os.path.exists(metadata_path):
                            with open(metadata_path, 'r', encoding='utf-8') as f:
                                metadata = json.load(f)
                            
                            print(f"      ✅ Metadata found:")
                            print(f"         Method: {metadata.get('method', 'unknown')}")
                            print(f"         Real video: {metadata.get('real_video', False)}")
                            print(f"         Title: {metadata.get('title', 'N/A')}")
                        
                        download_results.append({
                            'video_url': video['url'],
                            'filename': filename,
                            'size': actual_size,
                            'success': True,
                            'metadata': metadata if os.path.exists(metadata_path) else None
                        })
                    else:
                        print(f"      ❌ File not found: {full_path}")
                        download_results.append({
                            'video_url': video['url'],
                            'success': False,
                            'error': 'File not found'
                        })
                else:
                    print(f"      ❌ Download failed: {data['message']}")
                    download_results.append({
                        'video_url': video['url'],
                        'success': False,
                        'error': data['message']
                    })
            else:
                print(f"      ❌ Download request failed: {response.status_code}")
                download_results.append({
                    'video_url': video['url'],
                    'success': False,
                    'error': f'HTTP {response.status_code}'
                })
                
        except Exception as e:
            print(f"      ❌ Download error: {e}")
            download_results.append({
                'video_url': video['url'],
                'success': False,
                'error': str(e)
            })
        
        # Add delay between downloads
        time.sleep(2)
    
    # Step 4: Analyze Results
    print(f"\n4️⃣ Analyzing Real Integration Results...")
    
    successful_downloads = [r for r in download_results if r['success']]
    real_video_downloads = []
    fallback_downloads = []
    
    for result in successful_downloads:
        if result.get('metadata'):
            if result['metadata'].get('real_video', False):
                real_video_downloads.append(result)
            else:
                fallback_downloads.append(result)
        else:
            # Assume fallback if no metadata
            fallback_downloads.append(result)
    
    print(f"   Total downloads attempted: {len(download_results)}")
    print(f"   Successful downloads: {len(successful_downloads)}")
    print(f"   Real video downloads: {len(real_video_downloads)}")
    print(f"   Fallback downloads: {len(fallback_downloads)}")
    
    # Final Assessment
    print(f"\n" + "=" * 60)
    print(f"🎯 REAL DOUYIN INTEGRATION ASSESSMENT")
    print(f"=" * 60)
    
    # Translation Assessment
    print(f"✅ Translation: Working perfectly")
    
    # Search Assessment
    if real_douyin_count > 0:
        print(f"✅ Search: {real_douyin_count}/{len(videos)} real Douyin URLs found")
    else:
        print(f"⚠️ Search: Using fallback/curated content")
    
    # Download Assessment
    if len(real_video_downloads) > 0:
        print(f"🎉 Downloads: {len(real_video_downloads)} REAL videos downloaded!")
        for result in real_video_downloads:
            print(f"   • {result['filename']} ({result['size']:,} bytes)")
            if result.get('metadata'):
                print(f"     Method: {result['metadata'].get('method')}")
                print(f"     Title: {result['metadata'].get('title', 'N/A')}")
    else:
        print(f"⚠️ Downloads: Using intelligent fallback content")
        for result in fallback_downloads:
            print(f"   • {result['filename']} ({result['size']:,} bytes)")
    
    # Overall Assessment
    if len(real_video_downloads) > 0:
        print(f"\n🏆 SUCCESS: Real Douyin integration is working!")
        print(f"   • Real videos are being downloaded")
        print(f"   • yt-dlp or direct extraction is functional")
        print(f"   • Actual Douyin content is accessible")
    elif len(successful_downloads) > 0:
        print(f"\n⚠️ PARTIAL SUCCESS: Fallback system is working")
        print(f"   • Downloads are successful but using fallback content")
        print(f"   • Real Douyin extraction needs improvement")
        print(f"   • System gracefully handles failures")
    else:
        print(f"\n❌ NEEDS WORK: Download system has issues")
        print(f"   • Downloads are failing")
        print(f"   • Need to debug the download pipeline")
    
    return len(successful_downloads) > 0

def show_next_steps():
    """Show next steps based on results"""
    print(f"\n🚀 NEXT STEPS FOR REAL DOUYIN INTEGRATION:")
    print(f"=" * 50)
    
    print(f"1. 🔍 Improve Real Search:")
    print(f"   • Integrate actual Douyin search APIs")
    print(f"   • Use web scraping with better anti-detection")
    print(f"   • Add more real Douyin URL sources")
    
    print(f"\n2. 🎥 Enhance Video Extraction:")
    print(f"   • Update yt-dlp to latest version")
    print(f"   • Add more extraction methods")
    print(f"   • Implement better anti-scraping bypass")
    
    print(f"\n3. 🛡️ Add Anti-Detection:")
    print(f"   • Rotate user agents and headers")
    print(f"   • Add proxy support")
    print(f"   • Implement rate limiting")
    
    print(f"\n4. 📊 Monitor Success Rate:")
    print(f"   • Track real vs fallback downloads")
    print(f"   • Log extraction success rates")
    print(f"   • Optimize based on metrics")

if __name__ == "__main__":
    print("🧪 Testing Real Douyin Integration Flow...")
    print("Make sure backend is running on port 8000!")
    
    time.sleep(2)
    
    success = test_complete_real_douyin_flow()
    
    if success:
        show_next_steps()
        print(f"\n🎉 REAL DOUYIN INTEGRATION TEST COMPLETED! 🎉")
    else:
        print(f"\n❌ Integration test failed. Check the logs above.")
        print(f"Make sure both frontend and backend are running.")
