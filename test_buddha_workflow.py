#!/usr/bin/env python3
"""
Complete Buddha Workflow Test
This script tests the entire workflow: translate "Buddha" → search videos → download
"""

import requests
import json
import time
import os

API_BASE = "http://localhost:8000"

def print_step(step, message):
    print(f"\n🔸 Step {step}: {message}")
    print("=" * 50)

def print_success(message):
    print(f"✅ {message}")

def print_error(message):
    print(f"❌ {message}")

def test_complete_buddha_workflow():
    """Test the complete workflow with Buddha"""
    
    print("🧘 BUDDHA WORKFLOW TEST 🧘")
    print("Testing complete translation → search → download workflow")
    print("=" * 60)
    
    # Step 1: Translate "Buddha" to Chinese
    print_step(1, "Translating 'Buddha' to Chinese")
    
    try:
        translate_response = requests.post(
            f"{API_BASE}/api/translate",
            headers={"Content-Type": "application/json"},
            json={"text": "Buddha", "source_language": "en"}
        )
        
        if translate_response.status_code == 200:
            translation_result = translate_response.json()
            chinese_text = translation_result['translated_text']
            print_success(f"Translation successful!")
            print(f"   English: Buddha")
            print(f"   Chinese: {chinese_text}")
            print(f"   Source Language: {translation_result['source_language']}")
        else:
            print_error(f"Translation failed: {translate_response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Translation error: {e}")
        return False
    
    # Step 2: Search for Buddha videos
    print_step(2, f"Searching for videos with '{chinese_text}'")
    
    try:
        search_response = requests.post(
            f"{API_BASE}/api/search",
            headers={"Content-Type": "application/json"},
            json={"query": chinese_text, "limit": 3}
        )
        
        if search_response.status_code == 200:
            search_result = search_response.json()
            videos = search_result['videos']
            print_success(f"Found {len(videos)} Buddha videos!")
            
            for i, video in enumerate(videos, 1):
                print(f"   {i}. {video['title']}")
                print(f"      Author: {video['author']}")
                print(f"      Duration: {video['duration']}")
                print(f"      URL: {video['url']}")
                print()
        else:
            print_error(f"Search failed: {search_response.status_code}")
            return False
            
    except Exception as e:
        print_error(f"Search error: {e}")
        return False
    
    # Step 3: Download the first video
    if videos:
        print_step(3, f"Downloading first Buddha video")
        
        first_video = videos[0]
        try:
            download_response = requests.post(
                f"{API_BASE}/api/download",
                headers={"Content-Type": "application/json"},
                json={"video_url": first_video['url'], "quality": "best"}
            )
            
            if download_response.status_code == 200:
                download_result = download_response.json()
                
                if download_result['success']:
                    print_success("Download successful!")
                    print(f"   Title: {download_result.get('title', 'Unknown')}")
                    print(f"   File Path: {download_result.get('file_path', 'Unknown')}")
                    print(f"   File Size: {download_result.get('file_size', 'Unknown')}")
                    print(f"   Message: {download_result.get('message', '')}")
                    
                    # Check if file exists
                    file_path = download_result.get('file_path')
                    if file_path and os.path.exists(file_path):
                        print_success(f"File confirmed to exist at: {file_path}")
                        
                        # Show file content preview
                        try:
                            with open(file_path, 'r', encoding='utf-8') as f:
                                content = f.read()[:200]  # First 200 characters
                            print(f"   Content preview: {content}...")
                        except Exception as e:
                            print(f"   Could not read file content: {e}")
                    else:
                        print_error(f"File not found at: {file_path}")
                        
                else:
                    print_error(f"Download failed: {download_result.get('message', 'Unknown error')}")
                    return False
            else:
                print_error(f"Download request failed: {download_response.status_code}")
                return False
                
        except Exception as e:
            print_error(f"Download error: {e}")
            return False
    
    # Step 4: Summary
    print_step(4, "Workflow Summary")
    print_success("Complete Buddha workflow test PASSED! 🎉")
    print()
    print("📋 What was accomplished:")
    print("   ✅ Translated 'Buddha' from English to Chinese")
    print("   ✅ Searched for Buddha-related videos on Douyin")
    print("   ✅ Successfully downloaded a Buddha video/content")
    print("   ✅ Verified file creation and content")
    print()
    print("🌟 The Douyin Translator App is working perfectly!")
    print("🧘 May this bring wisdom and peace to all users.")
    
    return True

def test_additional_features():
    """Test additional features"""
    print("\n" + "=" * 60)
    print("🔧 TESTING ADDITIONAL FEATURES")
    print("=" * 60)
    
    # Test health check
    print_step("A", "Testing API Health Check")
    try:
        health_response = requests.get(f"{API_BASE}/api/health")
        if health_response.status_code == 200:
            health_data = health_response.json()
            print_success(f"API is healthy: {health_data}")
        else:
            print_error(f"Health check failed: {health_response.status_code}")
    except Exception as e:
        print_error(f"Health check error: {e}")
    
    # Test authentication
    print_step("B", "Testing Authentication")
    try:
        login_response = requests.post(
            f"{API_BASE}/api/auth/login",
            headers={"Content-Type": "application/json"},
            json={"email": "<EMAIL>", "password": "demo123"}
        )
        
        if login_response.status_code == 200:
            login_data = login_response.json()
            print_success("Authentication successful!")
            print(f"   Token type: {login_data['token_type']}")
            print(f"   Expires in: {login_data['expires_in']} seconds")
        else:
            print_error(f"Authentication failed: {login_response.status_code}")
    except Exception as e:
        print_error(f"Authentication error: {e}")

if __name__ == "__main__":
    print("🚀 Starting Complete Buddha Workflow Test")
    print("Make sure both frontend (localhost:3000) and backend (localhost:8000) are running!")
    print()
    
    # Wait a moment for user to read
    time.sleep(2)
    
    # Run the complete workflow test
    success = test_complete_buddha_workflow()
    
    if success:
        # Test additional features
        test_additional_features()
        
        print("\n" + "🎉" * 20)
        print("ALL TESTS PASSED SUCCESSFULLY!")
        print("The Douyin Translator App is fully functional!")
        print("🎉" * 20)
    else:
        print("\n" + "❌" * 20)
        print("SOME TESTS FAILED")
        print("Please check the backend logs for more details.")
        print("❌" * 20)
