google/cloud/translate/__init__.py,sha256=mExE2EfPSeU6FFR5UYc_5ZDAvL0uM2jH7K8TJ2psG9Y,5312
google/cloud/translate/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate/__pycache__/gapic_version.cpython-311.pyc,,
google/cloud/translate/gapic_version.py,sha256=7y3w3JjTqYv6yTmxpwhB9rACjtMyWAwEO8ZqIXsbJ-w,653
google/cloud/translate/py.typed,sha256=jrCRgjZWFb5Coh89vMrVar4BbIf9ukI_wgJa_bBOY9c,83
google/cloud/translate_v2/__init__.py,sha256=EXTO7rD-der2AXwqRtsuRHzqTHYb3xE831FUMCuCHrk,821
google/cloud/translate_v2/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v2/__pycache__/_http.cpython-311.pyc,,
google/cloud/translate_v2/__pycache__/client.cpython-311.pyc,,
google/cloud/translate_v2/_http.py,sha256=YRJhbWYoyH4ulU54G9ImunuQiXiuZCjbPHtjzqzJEGk,1731
google/cloud/translate_v2/client.py,sha256=0MkBwQi7l1nq4-QCGWhk8v2ZwtRv0LGLOwomCPKuwQA,10822
google/cloud/translate_v3/__init__.py,sha256=OyIY9jUldzudxizc7vPEv1lEJ5X7hLiSwio0cYHCwAU,4230
google/cloud/translate_v3/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v3/__pycache__/gapic_version.cpython-311.pyc,,
google/cloud/translate_v3/gapic_metadata.json,sha256=gIjXdZrBDwiJyxgcEVaGDHDCTCOEIOHsZxGpt4Zo8bc,4515
google/cloud/translate_v3/gapic_version.py,sha256=7y3w3JjTqYv6yTmxpwhB9rACjtMyWAwEO8ZqIXsbJ-w,653
google/cloud/translate_v3/py.typed,sha256=jrCRgjZWFb5Coh89vMrVar4BbIf9ukI_wgJa_bBOY9c,83
google/cloud/translate_v3/services/__init__.py,sha256=zWbvQwgy48VXnYBlD4x4jlBof70BFYtIC9fehwEW1WQ,600
google/cloud/translate_v3/services/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v3/services/translation_service/__init__.py,sha256=fZMllqRzdxjVN-2A04dhoDVsDaElHCDbkZuFNNh8Xwc,785
google/cloud/translate_v3/services/translation_service/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v3/services/translation_service/__pycache__/async_client.cpython-311.pyc,,
google/cloud/translate_v3/services/translation_service/__pycache__/client.cpython-311.pyc,,
google/cloud/translate_v3/services/translation_service/__pycache__/pagers.cpython-311.pyc,,
google/cloud/translate_v3/services/translation_service/async_client.py,sha256=DKjCfNsjN4pqD3hoOe9CM-jKzYEUArN1oqDIxDflnwc,68126
google/cloud/translate_v3/services/translation_service/client.py,sha256=YchXHICDbQz0Kr2WwyoIu_x7l8rbwRgMTjPU5C0Zbas,77259
google/cloud/translate_v3/services/translation_service/pagers.py,sha256=IQItugp2bNn8BlVSyUMgyaQWKygFefjo4584pN573-A,5906
google/cloud/translate_v3/services/translation_service/transports/__init__.py,sha256=xdKtNVCeie4vqSReQeIennPGYSqYAv-dAEDKxJdUMJ0,1482
google/cloud/translate_v3/services/translation_service/transports/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v3/services/translation_service/transports/__pycache__/base.cpython-311.pyc,,
google/cloud/translate_v3/services/translation_service/transports/__pycache__/grpc.cpython-311.pyc,,
google/cloud/translate_v3/services/translation_service/transports/__pycache__/grpc_asyncio.cpython-311.pyc,,
google/cloud/translate_v3/services/translation_service/transports/__pycache__/rest.cpython-311.pyc,,
google/cloud/translate_v3/services/translation_service/transports/base.py,sha256=qMkGpJ6QcBgPOLwlUh4qqVkNLo6-dY2UOZMpAlqg5uQ,12413
google/cloud/translate_v3/services/translation_service/transports/grpc.py,sha256=ZlMVbQBIkQx6ZzONZOWa_-Akf_9UCTgCfRZpSrK8oPg,24461
google/cloud/translate_v3/services/translation_service/transports/grpc_asyncio.py,sha256=TzvarxNnWYfJeEFJNbW_29CfzNZN5NrKlzFGt9uAb5c,24940
google/cloud/translate_v3/services/translation_service/transports/rest.py,sha256=NemZDe7UmL08S8AAtjH8xl9WmHZc7juz8dVB7fZmHCs,63444
google/cloud/translate_v3/types/__init__.py,sha256=2wRwT5-hjTdMZlQ-3BOE92excRtAbgOOaSK8GdN9a3o,2684
google/cloud/translate_v3/types/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v3/types/__pycache__/translation_service.cpython-311.pyc,,
google/cloud/translate_v3/types/translation_service.py,sha256=LeLSJSSRYa0GGavshGFnpwVNyOvtGPfm5MqHf-vbnZw,78878
google/cloud/translate_v3beta1/__init__.py,sha256=zacPPBSlhBlvAz2vjrpUdERrQ3VNtoL74t5zNEW2jTw,4235
google/cloud/translate_v3beta1/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v3beta1/__pycache__/gapic_version.cpython-311.pyc,,
google/cloud/translate_v3beta1/gapic_metadata.json,sha256=BZItpD605P00x7TP8tIz0q-ys0xSK_tAUKfplhVlCeg,4525
google/cloud/translate_v3beta1/gapic_version.py,sha256=7y3w3JjTqYv6yTmxpwhB9rACjtMyWAwEO8ZqIXsbJ-w,653
google/cloud/translate_v3beta1/py.typed,sha256=jrCRgjZWFb5Coh89vMrVar4BbIf9ukI_wgJa_bBOY9c,83
google/cloud/translate_v3beta1/services/__init__.py,sha256=zWbvQwgy48VXnYBlD4x4jlBof70BFYtIC9fehwEW1WQ,600
google/cloud/translate_v3beta1/services/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v3beta1/services/translation_service/__init__.py,sha256=fZMllqRzdxjVN-2A04dhoDVsDaElHCDbkZuFNNh8Xwc,785
google/cloud/translate_v3beta1/services/translation_service/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v3beta1/services/translation_service/__pycache__/async_client.cpython-311.pyc,,
google/cloud/translate_v3beta1/services/translation_service/__pycache__/client.cpython-311.pyc,,
google/cloud/translate_v3beta1/services/translation_service/__pycache__/pagers.cpython-311.pyc,,
google/cloud/translate_v3beta1/services/translation_service/async_client.py,sha256=TjywVcRXJZWrcvN7Qi5NkgC8qymfcz9kmzY2Ze3SiVk,64467
google/cloud/translate_v3beta1/services/translation_service/client.py,sha256=nWcKzefvC6Y5kmcnA-v_vq5VXhTXKx1R-pMQauz1Ob8,73593
google/cloud/translate_v3beta1/services/translation_service/pagers.py,sha256=YwWMwrMRkR0I9x1Y3Uj8rWgrc3NnwxCNRUasXMV2mJ0,5951
google/cloud/translate_v3beta1/services/translation_service/transports/__init__.py,sha256=xdKtNVCeie4vqSReQeIennPGYSqYAv-dAEDKxJdUMJ0,1482
google/cloud/translate_v3beta1/services/translation_service/transports/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v3beta1/services/translation_service/transports/__pycache__/base.cpython-311.pyc,,
google/cloud/translate_v3beta1/services/translation_service/transports/__pycache__/grpc.cpython-311.pyc,,
google/cloud/translate_v3beta1/services/translation_service/transports/__pycache__/grpc_asyncio.cpython-311.pyc,,
google/cloud/translate_v3beta1/services/translation_service/transports/__pycache__/rest.cpython-311.pyc,,
google/cloud/translate_v3beta1/services/translation_service/transports/base.py,sha256=UQxa7Ng5uOf1WWqpzCdjEwYDMyLsKev6IP1VzaQXI9E,12423
google/cloud/translate_v3beta1/services/translation_service/transports/grpc.py,sha256=_uHu28WfxViTi2fLwRskob7-A0p4NNUVNWmXDHIGrKo,24516
google/cloud/translate_v3beta1/services/translation_service/transports/grpc_asyncio.py,sha256=Be7JR0Zjg8McemTanzWNM02DPWSZhUdXuxjy_0LZQoc,24995
google/cloud/translate_v3beta1/services/translation_service/transports/rest.py,sha256=EdkpPFReNWejowQRNP1sWSdQEBblclnK0bylp-0ErDY,63544
google/cloud/translate_v3beta1/types/__init__.py,sha256=2wRwT5-hjTdMZlQ-3BOE92excRtAbgOOaSK8GdN9a3o,2684
google/cloud/translate_v3beta1/types/__pycache__/__init__.cpython-311.pyc,,
google/cloud/translate_v3beta1/types/__pycache__/translation_service.cpython-311.pyc,,
google/cloud/translate_v3beta1/types/translation_service.py,sha256=h2Oh77nnNR9r8v6Urx8a_8dTUdtDlF0sryqM2UDOKsw,78488
google_cloud_translate-3.12.1-py3.9-nspkg.pth,sha256=b0D5dZk3RUzK54tZ9iZDvLm7u8ltc5EzYrGCmhsuoNw,1698
google_cloud_translate-3.12.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
google_cloud_translate-3.12.1.dist-info/LICENSE,sha256=z8d0m5b2O9McPEK1xHG_dWgUBT6EfBDz6wA0F7xSPTA,11358
google_cloud_translate-3.12.1.dist-info/METADATA,sha256=YEszTIKbQq5Ptu5jlh1ALu6xjaJ4P_gZ9McS8U_sBhc,5213
google_cloud_translate-3.12.1.dist-info/RECORD,,
google_cloud_translate-3.12.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
google_cloud_translate-3.12.1.dist-info/WHEEL,sha256=bb2Ot9scclHKMOLDEHY6B2sicWOgugjFKaJsT7vwMQo,110
google_cloud_translate-3.12.1.dist-info/namespace_packages.txt,sha256=v8IaYqRE2a0onAGJIpZeFkkH83wXSWZRR9eOyfMwoTc,20
google_cloud_translate-3.12.1.dist-info/top_level.txt,sha256=_1QvSJIhFAGfxb79D6DhB7SUw2X6T4rwnz_LLrbcD3c,7
