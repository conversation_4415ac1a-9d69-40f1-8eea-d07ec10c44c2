{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Tools/Auto%20download%20Douyin/frontend/src/utils/api.ts"], "sourcesContent": ["import axios from 'axios';\nimport { \n  TranslateRequest, \n  TranslateResponse, \n  SearchRequest, \n  SearchResponse, \n  DownloadRequest, \n  DownloadResponse \n} from '@/types';\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';\n\nconst api = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor for logging\napi.interceptors.request.use(\n  (config) => {\n    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);\n    return config;\n  },\n  (error) => {\n    console.error('Request error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor for error handling\napi.interceptors.response.use(\n  (response) => {\n    return response;\n  },\n  (error) => {\n    console.error('Response error:', error);\n    if (error.response?.data?.detail) {\n      throw new Error(error.response.data.detail);\n    }\n    throw new Error(error.message || 'An unexpected error occurred');\n  }\n);\n\nexport const translateText = async (request: TranslateRequest): Promise<TranslateResponse> => {\n  try {\n    const response = await api.post<TranslateResponse>('/api/translate', request);\n    return response.data;\n  } catch (error) {\n    console.error('Translation API error:', error);\n    throw error;\n  }\n};\n\nexport const searchVideos = async (request: SearchRequest): Promise<SearchResponse> => {\n  try {\n    const response = await api.post<SearchResponse>('/api/search', request);\n    return response.data;\n  } catch (error) {\n    console.error('Search API error:', error);\n    throw error;\n  }\n};\n\nexport const downloadVideo = async (request: DownloadRequest): Promise<DownloadResponse> => {\n  try {\n    const response = await api.post<DownloadResponse>('/api/download', request);\n    return response.data;\n  } catch (error) {\n    console.error('Download API error:', error);\n    throw error;\n  }\n};\n\nexport const getDownloadedFile = (filename: string): string => {\n  return `${API_BASE_URL}/api/download/${filename}`;\n};\n\nexport const checkHealth = async (): Promise<{ status: string; services: string }> => {\n  try {\n    const response = await api.get('/api/health');\n    return response.data;\n  } catch (error) {\n    console.error('Health check error:', error);\n    throw error;\n  }\n};\n\nexport default api;\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAUA,MAAM,eAAe,6DAAmC;AAExD,MAAM,MAAM,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACvB,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,kCAAkC;AAClC,IAAI,YAAY,CAAC,OAAO,CAAC,GAAG,CAC1B,CAAC;IACC,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,MAAM,EAAE,cAAc,YAAY,EAAE,OAAO,GAAG,EAAE;IAC7E,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,kBAAkB;IAChC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,0CAA0C;AAC1C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC3B,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,mBAAmB;IACjC,IAAI,MAAM,QAAQ,EAAE,MAAM,QAAQ;QAChC,MAAM,IAAI,MAAM,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM;IAC5C;IACA,MAAM,IAAI,MAAM,MAAM,OAAO,IAAI;AACnC;AAGK,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAoB,kBAAkB;QACrE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAiB,eAAe;QAC/D,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qBAAqB;QACnC,MAAM;IACR;AACF;AAEO,MAAM,gBAAgB,OAAO;IAClC,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,IAAI,CAAmB,iBAAiB;QACnE,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAEO,MAAM,oBAAoB,CAAC;IAChC,OAAO,GAAG,aAAa,cAAc,EAAE,UAAU;AACnD;AAEO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;QAC/B,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Tools/Auto%20download%20Douyin/frontend/src/components/TranslationSection.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { toast } from 'react-hot-toast';\nimport { Languages, ArrowRight, Loader2 } from 'lucide-react';\nimport { translateText } from '@/utils/api';\n\ninterface TranslationSectionProps {\n  onTranslationComplete: (translatedText: string) => void;\n  isLoading: boolean;\n  setIsLoading: (loading: boolean) => void;\n}\n\nexport default function TranslationSection({ \n  onTranslationComplete, \n  isLoading, \n  setIsLoading \n}: TranslationSectionProps) {\n  const [inputText, setInputText] = useState<string>('');\n  const [translatedText, setTranslatedText] = useState<string>('');\n  const [sourceLanguage, setSourceLanguage] = useState<string>('auto');\n\n  const handleTranslate = async () => {\n    if (!inputText.trim()) {\n      toast.error('Please enter some text to translate');\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      const result = await translateText({\n        text: inputText,\n        source_language: sourceLanguage\n      });\n\n      setTranslatedText(result.translated_text);\n      onTranslationComplete(result.translated_text);\n      \n      toast.success(`Translated from ${result.source_language} to Chinese!`);\n    } catch (error) {\n      console.error('Translation error:', error);\n      toast.error(error instanceof Error ? error.message : 'Translation failed');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && e.ctrlKey) {\n      handleTranslate();\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 md:p-8\">\n      <div className=\"flex items-center gap-3 mb-6\">\n        <Languages className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" />\n        <h2 className=\"text-2xl font-bold text-gray-800 dark:text-white\">\n          Text Translation\n        </h2>\n      </div>\n\n      <div className=\"grid md:grid-cols-2 gap-6\">\n        {/* Input Section */}\n        <div className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Source Language\n            </label>\n            <select\n              value={sourceLanguage}\n              onChange={(e) => setSourceLanguage(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value=\"auto\">Auto-detect</option>\n              <option value=\"en\">English</option>\n              <option value=\"ja\">Japanese</option>\n              <option value=\"ko\">Korean</option>\n              <option value=\"es\">Spanish</option>\n              <option value=\"fr\">French</option>\n              <option value=\"de\">German</option>\n              <option value=\"it\">Italian</option>\n              <option value=\"pt\">Portuguese</option>\n              <option value=\"ru\">Russian</option>\n              <option value=\"ar\">Arabic</option>\n              <option value=\"hi\">Hindi</option>\n              <option value=\"th\">Thai</option>\n              <option value=\"vi\">Vietnamese</option>\n            </select>\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Text to Translate\n            </label>\n            <textarea\n              value={inputText}\n              onChange={(e) => setInputText(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Enter text in any language...\"\n              rows={6}\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none\"\n            />\n            <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n              Press Ctrl+Enter to translate\n            </p>\n          </div>\n\n          <button\n            onClick={handleTranslate}\n            disabled={isLoading || !inputText.trim()}\n            className=\"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2\"\n          >\n            {isLoading ? (\n              <>\n                <Loader2 className=\"w-4 h-4 animate-spin\" />\n                Translating...\n              </>\n            ) : (\n              <>\n                <Languages className=\"w-4 h-4\" />\n                Translate to Chinese\n                <ArrowRight className=\"w-4 h-4\" />\n              </>\n            )}\n          </button>\n        </div>\n\n        {/* Output Section */}\n        <div className=\"space-y-4\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Chinese Translation\n            </label>\n            <div className=\"w-full h-[200px] px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white overflow-y-auto\">\n              {translatedText ? (\n                <p className=\"whitespace-pre-wrap\">{translatedText}</p>\n              ) : (\n                <p className=\"text-gray-500 dark:text-gray-400 italic\">\n                  Translation will appear here...\n                </p>\n              )}\n            </div>\n          </div>\n\n          {translatedText && (\n            <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n              <p className=\"text-sm text-green-800 dark:text-green-200\">\n                ✅ Translation complete! You can now search for Douyin videos using this Chinese text.\n              </p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAae,SAAS,mBAAmB,EACzC,qBAAqB,EACrB,SAAS,EACT,YAAY,EACY;IACxB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,MAAM,kBAAkB;QACtB,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE;gBACjC,MAAM;gBACN,iBAAiB;YACnB;YAEA,kBAAkB,OAAO,eAAe;YACxC,sBAAsB,OAAO,eAAe;YAE5C,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,gBAAgB,EAAE,OAAO,eAAe,CAAC,YAAY,CAAC;QACvE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,EAAE,OAAO,EAAE;YAClC;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;kCACrB,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;;;;;;;0BAKnE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wCACjD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAO;;;;;;0DACrB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;0DACnB,8OAAC;gDAAO,OAAM;0DAAK;;;;;;;;;;;;;;;;;;0CAIvB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,YAAY;wCACZ,aAAY;wCACZ,MAAM;wCACN,WAAU;;;;;;kDAEZ,8OAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;0CAK/D,8OAAC;gCACC,SAAS;gCACT,UAAU,aAAa,CAAC,UAAU,IAAI;gCACtC,WAAU;0CAET,0BACC;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAAyB;;iEAI9C;;sDACE,8OAAC,4MAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAY;sDAEjC,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;kCAO9B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCAAI,WAAU;kDACZ,+BACC,8OAAC;4CAAE,WAAU;sDAAuB;;;;;iEAEpC,8OAAC;4CAAE,WAAU;sDAA0C;;;;;;;;;;;;;;;;;4BAO5D,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxE", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Tools/Auto%20download%20Douyin/frontend/src/components/SearchSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { toast } from \"react-hot-toast\";\nimport { Search, Loader2, Video } from \"lucide-react\";\nimport { searchVideos } from \"@/utils/api\";\nimport { VideoInfo } from \"@/types\";\n\ninterface SearchSectionProps {\n  translatedText: string;\n  onSearchComplete: (videos: VideoInfo[]) => void;\n  isLoading: boolean;\n  setIsLoading: (loading: boolean) => void;\n}\n\nexport default function SearchSection({\n  translatedText,\n  onSearchComplete,\n  isLoading,\n  setIsLoading,\n}: SearchSectionProps) {\n  const [searchQuery, setSearchQuery] = useState<string>(translatedText);\n  const [searchLimit, setSearchLimit] = useState<number>(10);\n\n  // Update search query when translatedText changes\n  useEffect(() => {\n    setSearchQuery(translatedText);\n  }, [translatedText]);\n\n  const handleSearch = async () => {\n    if (!searchQuery.trim()) {\n      toast.error(\"Please enter a search query\");\n      return;\n    }\n\n    setIsLoading(true);\n    try {\n      const result = await searchVideos({\n        query: searchQuery,\n        limit: searchLimit,\n      });\n\n      onSearchComplete(result.videos);\n\n      if (result.videos.length === 0) {\n        toast.error(\"No videos found for this search query\");\n      } else {\n        toast.success(`Found ${result.videos.length} videos!`);\n      }\n    } catch (error) {\n      console.error(\"Search error:\", error);\n      toast.error(error instanceof Error ? error.message : \"Search failed\");\n      onSearchComplete([]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === \"Enter\") {\n      handleSearch();\n    }\n  };\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 md:p-8\">\n      <div className=\"flex items-center gap-3 mb-6\">\n        <Video className=\"w-6 h-6 text-purple-600 dark:text-purple-400\" />\n        <h2 className=\"text-2xl font-bold text-gray-800 dark:text-white\">\n          Search Douyin Videos\n        </h2>\n      </div>\n\n      <div className=\"space-y-4\">\n        <div className=\"grid md:grid-cols-4 gap-4\">\n          <div className=\"md:col-span-3\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Search Query (Chinese)\n            </label>\n            <input\n              type=\"text\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Enter Chinese keywords to search...\"\n              className=\"w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400\"\n            />\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Results Limit\n            </label>\n            <select\n              value={searchLimit}\n              onChange={(e) => setSearchLimit(Number(e.target.value))}\n              className=\"w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n            >\n              <option value={5}>5 videos</option>\n              <option value={10}>10 videos</option>\n              <option value={15}>15 videos</option>\n              <option value={20}>20 videos</option>\n            </select>\n          </div>\n        </div>\n\n        <button\n          onClick={handleSearch}\n          disabled={isLoading || !searchQuery.trim()}\n          className=\"w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2\"\n        >\n          {isLoading ? (\n            <>\n              <Loader2 className=\"w-4 h-4 animate-spin\" />\n              Searching Douyin...\n            </>\n          ) : (\n            <>\n              <Search className=\"w-4 h-4\" />\n              Search Videos\n            </>\n          )}\n        </button>\n\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4\">\n          <p className=\"text-sm text-blue-800 dark:text-blue-200\">\n            💡 <strong>Tip:</strong> Use Chinese keywords for better search\n            results. The search will look for videos on Douyin (TikTok China)\n            matching your query.\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AALA;;;;;;AAee,SAAS,cAAc,EACpC,cAAc,EACd,gBAAgB,EAChB,SAAS,EACT,YAAY,EACO;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe;IACjB,GAAG;QAAC;KAAe;IAEnB,MAAM,eAAe;QACnB,IAAI,CAAC,YAAY,IAAI,IAAI;YACvB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE;gBAChC,OAAO;gBACP,OAAO;YACT;YAEA,iBAAiB,OAAO,MAAM;YAE9B,IAAI,OAAO,MAAM,CAAC,MAAM,KAAK,GAAG;gBAC9B,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,OAAO;gBACL,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YACvD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACrD,iBAAiB,EAAE;QACrB,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAG,WAAU;kCAAmD;;;;;;;;;;;;0BAKnE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,YAAY;wCACZ,aAAY;wCACZ,WAAU;;;;;;;;;;;;0CAId,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;wCACrD,WAAU;;0DAEV,8OAAC;gDAAO,OAAO;0DAAG;;;;;;0DAClB,8OAAC;gDAAO,OAAO;0DAAI;;;;;;0DACnB,8OAAC;gDAAO,OAAO;0DAAI;;;;;;0DACnB,8OAAC;gDAAO,OAAO;0DAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAKzB,8OAAC;wBACC,SAAS;wBACT,UAAU,aAAa,CAAC,YAAY,IAAI;wBACxC,WAAU;kCAET,0BACC;;8CACE,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAAyB;;yDAI9C;;8CACE,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;kCAMpC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAA2C;8CACnD,8OAAC;8CAAO;;;;;;gCAAa;;;;;;;;;;;;;;;;;;;;;;;;AAQpC", "debugId": null}}, {"offset": {"line": 839, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Tools/Auto%20download%20Douyin/frontend/src/components/VideoResults.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { toast } from \"react-hot-toast\";\nimport {\n  Download,\n  ExternalLink,\n  Clock,\n  User,\n  Loader2,\n  CheckCircle,\n} from \"lucide-react\";\nimport { downloadVideo } from \"@/utils/api\";\nimport { VideoInfo } from \"@/types\";\n\ninterface VideoResultsProps {\n  videos: VideoInfo[];\n  isLoading: boolean;\n  setIsLoading: (loading: boolean) => void;\n}\n\nexport default function VideoResults({\n  videos,\n  isLoading,\n  setIsLoading,\n}: VideoResultsProps) {\n  const [downloadingVideos, setDownloadingVideos] = useState<Set<string>>(\n    new Set()\n  );\n  const [downloadedVideos, setDownloadedVideos] = useState<Set<string>>(\n    new Set()\n  );\n\n  const handleDownload = async (video: VideoInfo) => {\n    if (!video.url) {\n      toast.error(\"Video URL not available\");\n      return;\n    }\n\n    setDownloadingVideos((prev) => new Set(prev).add(video.url));\n\n    try {\n      const result = await downloadVideo({\n        video_url: video.url,\n        quality: \"best\",\n      });\n\n      if (result.success) {\n        setDownloadedVideos((prev) => new Set(prev).add(video.url));\n        toast.success(`Downloaded: ${video.title}`);\n\n        if (result.file_path) {\n          // Extract filename from path\n          const filename =\n            result.file_path.split(\"/\").pop() ||\n            result.file_path.split(\"\\\\\").pop();\n\n          if (filename) {\n            // Create a download link for the user\n            const link = document.createElement(\"a\");\n            link.href = `http://localhost:8000/api/download-file/${filename}`;\n            link.download = filename;\n            link.target = \"_blank\";\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n\n            // Show additional success message with file info\n            toast.success(`File ready: ${filename} (${result.file_size})`, {\n              duration: 5000,\n            });\n          }\n        }\n      } else {\n        toast.error(result.message || \"Download failed\");\n      }\n    } catch (error) {\n      console.error(\"Download error:\", error);\n      toast.error(error instanceof Error ? error.message : \"Download failed\");\n    } finally {\n      setDownloadingVideos((prev) => {\n        const newSet = new Set(prev);\n        newSet.delete(video.url);\n        return newSet;\n      });\n    }\n  };\n\n  const isVideoDownloading = (videoUrl: string) =>\n    downloadingVideos.has(videoUrl);\n  const isVideoDownloaded = (videoUrl: string) =>\n    downloadedVideos.has(videoUrl);\n\n  return (\n    <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 md:p-8\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <div className=\"flex items-center gap-3\">\n          <Download className=\"w-6 h-6 text-green-600 dark:text-green-400\" />\n          <h2 className=\"text-2xl font-bold text-gray-800 dark:text-white\">\n            Video Results\n          </h2>\n        </div>\n        <span className=\"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium\">\n          {videos.length} videos found\n        </span>\n      </div>\n\n      <div className=\"grid gap-4 md:gap-6\">\n        {videos.map((video, index) => (\n          <div\n            key={`${video.url}-${index}`}\n            className=\"border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow duration-200\"\n          >\n            <div className=\"flex flex-col md:flex-row gap-4\">\n              {/* Video Thumbnail */}\n              <div className=\"flex-shrink-0\">\n                {video.thumbnail ? (\n                  <img\n                    src={video.thumbnail}\n                    alt={video.title}\n                    className=\"w-full md:w-32 h-24 object-cover rounded-lg\"\n                    onError={(e) => {\n                      const target = e.target as HTMLImageElement;\n                      target.style.display = \"none\";\n                    }}\n                  />\n                ) : (\n                  <div className=\"w-full md:w-32 h-24 bg-gray-200 dark:bg-gray-700 rounded-lg flex items-center justify-center\">\n                    <Download className=\"w-8 h-8 text-gray-400\" />\n                  </div>\n                )}\n              </div>\n\n              {/* Video Info */}\n              <div className=\"flex-grow\">\n                <h3 className=\"text-lg font-semibold text-gray-800 dark:text-white mb-2 line-clamp-2\">\n                  {video.title}\n                </h3>\n\n                <div className=\"flex flex-wrap gap-4 text-sm text-gray-600 dark:text-gray-400 mb-3\">\n                  {video.author && (\n                    <div className=\"flex items-center gap-1\">\n                      <User className=\"w-4 h-4\" />\n                      <span>{video.author}</span>\n                    </div>\n                  )}\n                  {video.duration && (\n                    <div className=\"flex items-center gap-1\">\n                      <Clock className=\"w-4 h-4\" />\n                      <span>{video.duration}</span>\n                    </div>\n                  )}\n                </div>\n\n                {/* Action Buttons */}\n                <div className=\"flex flex-wrap gap-2\">\n                  <button\n                    onClick={() => handleDownload(video)}\n                    disabled={\n                      isVideoDownloading(video.url) ||\n                      isVideoDownloaded(video.url)\n                    }\n                    className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2 ${\n                      isVideoDownloaded(video.url)\n                        ? \"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 cursor-not-allowed\"\n                        : isVideoDownloading(video.url)\n                        ? \"bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 cursor-not-allowed\"\n                        : \"bg-green-600 hover:bg-green-700 text-white\"\n                    }`}\n                  >\n                    {isVideoDownloaded(video.url) ? (\n                      <>\n                        <CheckCircle className=\"w-4 h-4\" />\n                        Downloaded\n                      </>\n                    ) : isVideoDownloading(video.url) ? (\n                      <>\n                        <Loader2 className=\"w-4 h-4 animate-spin\" />\n                        Downloading...\n                      </>\n                    ) : (\n                      <>\n                        <Download className=\"w-4 h-4\" />\n                        Download\n                      </>\n                    )}\n                  </button>\n\n                  {video.url && (\n                    <a\n                      href={video.url}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors duration-200 flex items-center gap-2\"\n                    >\n                      <ExternalLink className=\"w-4 h-4\" />\n                      View Original\n                    </a>\n                  )}\n                </div>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      <div className=\"mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4\">\n        <p className=\"text-sm text-yellow-800 dark:text-yellow-200\">\n          ⚠️ <strong>Note:</strong> Video downloads may take some time depending\n          on the video size and quality. Please be patient and ensure you have\n          permission to download the content.\n        </p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAZA;;;;;;AAqBe,SAAS,aAAa,EACnC,MAAM,EACN,SAAS,EACT,YAAY,EACM;IAClB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD,IAAI;IAEN,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrD,IAAI;IAGN,MAAM,iBAAiB,OAAO;QAC5B,IAAI,CAAC,MAAM,GAAG,EAAE;YACd,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,qBAAqB,CAAC,OAAS,IAAI,IAAI,MAAM,GAAG,CAAC,MAAM,GAAG;QAE1D,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE;gBACjC,WAAW,MAAM,GAAG;gBACpB,SAAS;YACX;YAEA,IAAI,OAAO,OAAO,EAAE;gBAClB,oBAAoB,CAAC,OAAS,IAAI,IAAI,MAAM,GAAG,CAAC,MAAM,GAAG;gBACzD,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,MAAM,KAAK,EAAE;gBAE1C,IAAI,OAAO,SAAS,EAAE;oBACpB,6BAA6B;oBAC7B,MAAM,WACJ,OAAO,SAAS,CAAC,KAAK,CAAC,KAAK,GAAG,MAC/B,OAAO,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG;oBAElC,IAAI,UAAU;wBACZ,sCAAsC;wBACtC,MAAM,OAAO,SAAS,aAAa,CAAC;wBACpC,KAAK,IAAI,GAAG,CAAC,wCAAwC,EAAE,UAAU;wBACjE,KAAK,QAAQ,GAAG;wBAChB,KAAK,MAAM,GAAG;wBACd,SAAS,IAAI,CAAC,WAAW,CAAC;wBAC1B,KAAK,KAAK;wBACV,SAAS,IAAI,CAAC,WAAW,CAAC;wBAE1B,iDAAiD;wBACjD,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE,OAAO,SAAS,CAAC,CAAC,CAAC,EAAE;4BAC7D,UAAU;wBACZ;oBACF;gBACF;YACF,OAAO;gBACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,OAAO,IAAI;YAChC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QACvD,SAAU;YACR,qBAAqB,CAAC;gBACpB,MAAM,SAAS,IAAI,IAAI;gBACvB,OAAO,MAAM,CAAC,MAAM,GAAG;gBACvB,OAAO;YACT;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC,WAC1B,kBAAkB,GAAG,CAAC;IACxB,MAAM,oBAAoB,CAAC,WACzB,iBAAiB,GAAG,CAAC;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,8OAAC;gCAAG,WAAU;0CAAmD;;;;;;;;;;;;kCAInE,8OAAC;wBAAK,WAAU;;4BACb,OAAO,MAAM;4BAAC;;;;;;;;;;;;;0BAInB,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC;wBAEC,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,MAAM,SAAS,iBACd,8OAAC;wCACC,KAAK,MAAM,SAAS;wCACpB,KAAK,MAAM,KAAK;wCAChB,WAAU;wCACV,SAAS,CAAC;4CACR,MAAM,SAAS,EAAE,MAAM;4CACvB,OAAO,KAAK,CAAC,OAAO,GAAG;wCACzB;;;;;6DAGF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAM1B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;gDACZ,MAAM,MAAM,kBACX,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;sEAAM,MAAM,MAAM;;;;;;;;;;;;gDAGtB,MAAM,QAAQ,kBACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;sEAAM,MAAM,QAAQ;;;;;;;;;;;;;;;;;;sDAM3B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,eAAe;oDAC9B,UACE,mBAAmB,MAAM,GAAG,KAC5B,kBAAkB,MAAM,GAAG;oDAE7B,WAAW,CAAC,wFAAwF,EAClG,kBAAkB,MAAM,GAAG,IACvB,4FACA,mBAAmB,MAAM,GAAG,IAC5B,qFACA,8CACJ;8DAED,kBAAkB,MAAM,GAAG,kBAC1B;;0EACE,8OAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;4DAAY;;uEAGnC,mBAAmB,MAAM,GAAG,kBAC9B;;0EACE,8OAAC,iNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;4DAAyB;;qFAI9C;;0EACE,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;4DAAY;;;;;;;;gDAMrC,MAAM,GAAG,kBACR,8OAAC;oDACC,MAAM,MAAM,GAAG;oDACf,QAAO;oDACP,KAAI;oDACJ,WAAU;;sEAEV,8OAAC,sNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;wDAAY;;;;;;;;;;;;;;;;;;;;;;;;;uBArFzC,GAAG,MAAM,GAAG,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;0BAgGlC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAA+C;sCACvD,8OAAC;sCAAO;;;;;;wBAAc;;;;;;;;;;;;;;;;;;AAOnC", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Tools/Auto%20download%20Douyin/frontend/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Toaster } from \"react-hot-toast\";\nimport TranslationSection from \"@/components/TranslationSection\";\nimport SearchSection from \"@/components/SearchSection\";\nimport VideoResults from \"@/components/VideoResults\";\nimport { VideoInfo } from \"@/types\";\n\nexport default function Home() {\n  const [translatedText, setTranslatedText] = useState<string>(\"\");\n  const [videos, setVideos] = useState<VideoInfo[]>([]);\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-slate-900 dark:to-gray-800\">\n      <Toaster\n        position=\"top-right\"\n        toastOptions={{\n          duration: 4000,\n          style: {\n            background: \"#363636\",\n            color: \"#fff\",\n          },\n          success: {\n            duration: 3000,\n            iconTheme: {\n              primary: \"#10b981\",\n              secondary: \"#fff\",\n            },\n          },\n          error: {\n            duration: 5000,\n            iconTheme: {\n              primary: \"#ef4444\",\n              secondary: \"#fff\",\n            },\n          },\n        }}\n      />\n\n      <div className=\"container mx-auto px-4 py-6 md:py-12\">\n        {/* Header */}\n        <header className=\"text-center mb-8 md:mb-16\">\n          <div className=\"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl mb-6\">\n            <span className=\"text-2xl font-bold text-white\">抖</span>\n          </div>\n          <h1 className=\"text-3xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent mb-4\">\n            Douyin Translator\n          </h1>\n          <p className=\"text-base md:text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            🚀 Translate any text to Chinese and discover amazing Douyin videos.\n            <br />\n            📥 Download your favorites with just one click! Fast, reliable, and\n            easy to use.\n          </p>\n\n          {/* Status Indicators */}\n          <div className=\"flex flex-wrap justify-center gap-3 mt-6\">\n            <div className=\"inline-flex items-center gap-2 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              Translation Active\n            </div>\n            <div className=\"inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium\">\n              <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"></div>\n              Search Ready\n            </div>\n            <div className=\"inline-flex items-center gap-2 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium\">\n              <div className=\"w-2 h-2 bg-purple-500 rounded-full animate-pulse\"></div>\n              Download Ready\n            </div>\n          </div>\n        </header>\n\n        {/* Progress Indicator */}\n        {isLoading && (\n          <div className=\"fixed top-0 left-0 w-full h-1 bg-gray-200 dark:bg-gray-700 z-50\">\n            <div className=\"h-full bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse\"></div>\n          </div>\n        )}\n\n        {/* Main Content */}\n        <div className=\"max-w-6xl mx-auto space-y-6 md:space-y-8\">\n          {/* Translation Section */}\n          <div className=\"transform transition-all duration-300 hover:scale-[1.01]\">\n            <TranslationSection\n              onTranslationComplete={setTranslatedText}\n              isLoading={isLoading}\n              setIsLoading={setIsLoading}\n            />\n          </div>\n\n          {/* Search Section */}\n          {translatedText && (\n            <div className=\"transform transition-all duration-300 hover:scale-[1.01]\">\n              <SearchSection\n                translatedText={translatedText}\n                onSearchComplete={setVideos}\n                isLoading={isLoading}\n                setIsLoading={setIsLoading}\n              />\n            </div>\n          )}\n\n          {/* Video Results */}\n          {videos.length > 0 && (\n            <div className=\"transform transition-all duration-300\">\n              <VideoResults\n                videos={videos}\n                isLoading={isLoading}\n                setIsLoading={setIsLoading}\n              />\n            </div>\n          )}\n\n          {/* Quick Start Guide */}\n          {!translatedText && !isLoading && (\n            <div className=\"bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-6 md:p-8\">\n              <h3 className=\"text-xl font-semibold text-gray-800 dark:text-white mb-4 text-center\">\n                🚀 Quick Start Guide\n              </h3>\n              <div className=\"grid md:grid-cols-3 gap-4 text-center\">\n                <div className=\"space-y-2\">\n                  <div className=\"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto\">\n                    <span className=\"text-xl\">1️⃣</span>\n                  </div>\n                  <h4 className=\"font-medium text-gray-800 dark:text-white\">\n                    Translate\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    Enter English text and get instant Chinese translation\n                  </p>\n                </div>\n                <div className=\"space-y-2\">\n                  <div className=\"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto\">\n                    <span className=\"text-xl\">2️⃣</span>\n                  </div>\n                  <h4 className=\"font-medium text-gray-800 dark:text-white\">\n                    Search\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    Find relevant Douyin videos using Chinese keywords\n                  </p>\n                </div>\n                <div className=\"space-y-2\">\n                  <div className=\"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto\">\n                    <span className=\"text-xl\">3️⃣</span>\n                  </div>\n                  <h4 className=\"font-medium text-gray-800 dark:text-white\">\n                    Download\n                  </h4>\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    Download videos directly to your device\n                  </p>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Enhanced Footer */}\n        <footer className=\"mt-16 md:mt-24 text-center\">\n          <div className=\"bg-white/30 dark:bg-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6 max-w-4xl mx-auto\">\n            <div className=\"flex flex-col md:flex-row items-center justify-between gap-4\">\n              <div className=\"text-gray-600 dark:text-gray-400\">\n                <p className=\"font-medium\">Douyin Translator App</p>\n                <p className=\"text-sm\">Built with ❤️ using Next.js & FastAPI</p>\n              </div>\n              <div className=\"flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400\">\n                <span className=\"flex items-center gap-1\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  API Online\n                </span>\n                <span>•</span>\n                <span>v1.0.0</span>\n                <span>•</span>\n                <span>&copy; 2024</span>\n              </div>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEpD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,uJAAA,CAAA,UAAO;gBACN,UAAS;gBACT,cAAc;oBACZ,UAAU;oBACV,OAAO;wBACL,YAAY;wBACZ,OAAO;oBACT;oBACA,SAAS;wBACP,UAAU;wBACV,WAAW;4BACT,SAAS;4BACT,WAAW;wBACb;oBACF;oBACA,OAAO;wBACL,UAAU;wBACV,WAAW;4BACT,SAAS;4BACT,WAAW;wBACb;oBACF;gBACF;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;;;;;;0CAElD,8OAAC;gCAAG,WAAU;0CAA+L;;;;;;0CAG7M,8OAAC;gCAAE,WAAU;;oCAA0F;kDAErG,8OAAC;;;;;oCAAK;;;;;;;0CAMR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;4CAAwD;;;;;;;kDAGzE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;4CAAuD;;;;;;;kDAGxE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;4CAAyD;;;;;;;;;;;;;;;;;;;oBAO7E,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAKnB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,wIAAA,CAAA,UAAkB;oCACjB,uBAAuB;oCACvB,WAAW;oCACX,cAAc;;;;;;;;;;;4BAKjB,gCACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,mIAAA,CAAA,UAAa;oCACZ,gBAAgB;oCAChB,kBAAkB;oCAClB,WAAW;oCACX,cAAc;;;;;;;;;;;4BAMnB,OAAO,MAAM,GAAG,mBACf,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;oCACX,QAAQ;oCACR,WAAW;oCACX,cAAc;;;;;;;;;;;4BAMnB,CAAC,kBAAkB,CAAC,2BACnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuE;;;;;;kDAGrF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;kEAE5B,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAG1D,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;0DAI1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;kEAE5B,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAG1D,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;0DAI1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;kEAE5B,8OAAC;wDAAG,WAAU;kEAA4C;;;;;;kEAG1D,8OAAC;wDAAE,WAAU;kEAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUlE,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,8OAAC;gDAAE,WAAU;0DAAU;;;;;;;;;;;;kDAEzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAI,WAAU;;;;;;oDAA0C;;;;;;;0DAG3D,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}]}