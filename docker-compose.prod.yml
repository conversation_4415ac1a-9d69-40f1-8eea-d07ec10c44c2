version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.prod
    environment:
      - NEXT_PUBLIC_API_URL=https://api.your-domain.com
    networks:
      - app-network
    restart: unless-stopped

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.prod
    environment:
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - DEBUG=False
      - ENVIRONMENT=production
      - REDIS_URL=redis://redis:6379/0
      - DATABASE_URL=********************************************/douyin_translator
    depends_on:
      - redis
      - postgres
    volumes:
      - downloads:/app/downloads
      - ./backend/.env.production:/app/.env
    networks:
      - app-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=douyin_translator
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - app-network
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.prod.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/ssl/certs
      - static_files:/var/www/static
    depends_on:
      - frontend
      - backend
    networks:
      - app-network
    restart: unless-stopped

  certbot:
    image: certbot/certbot
    volumes:
      - ./ssl:/etc/letsencrypt
      - ./ssl-challenge:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email <EMAIL> --agree-tos --no-eff-email -d your-domain.com -d www.your-domain.com

volumes:
  downloads:
  redis_data:
  postgres_data:
  static_files:

networks:
  app-network:
    driver: bridge
