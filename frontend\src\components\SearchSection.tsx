"use client";

import { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import { Search, Loader2, Video } from "lucide-react";
import { searchVideos } from "@/utils/api";
import { VideoInfo } from "@/types";

interface SearchSectionProps {
  translatedText: string;
  onSearchComplete: (videos: VideoInfo[]) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

export default function SearchSection({
  translatedText,
  onSearchComplete,
  isLoading,
  setIsLoading,
}: SearchSectionProps) {
  const [searchQuery, setSearchQuery] = useState<string>(translatedText);
  const [searchLimit, setSearchLimit] = useState<number>(10);

  // Update search query when translatedText changes
  useEffect(() => {
    setSearchQuery(translatedText);
  }, [translatedText]);

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      toast.error("Please enter a search query");
      return;
    }

    setIsLoading(true);
    try {
      const result = await searchVideos({
        query: searchQuery,
        limit: searchLimit,
      });

      onSearchComplete(result.videos);

      if (result.videos.length === 0) {
        toast.error("No videos found for this search query");
      } else {
        toast.success(`Found ${result.videos.length} videos!`);
      }
    } catch (error) {
      console.error("Search error:", error);
      toast.error(error instanceof Error ? error.message : "Search failed");
      onSearchComplete([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 md:p-8">
      <div className="flex items-center gap-3 mb-6">
        <Video className="w-6 h-6 text-purple-600 dark:text-purple-400" />
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
          Search Douyin Videos
        </h2>
      </div>

      <div className="space-y-4">
        <div className="grid md:grid-cols-4 gap-4">
          <div className="md:col-span-3">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search Query (Chinese)
            </label>
            <input
              type="text"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter Chinese keywords to search..."
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Results Limit
            </label>
            <select
              value={searchLimit}
              onChange={(e) => setSearchLimit(Number(e.target.value))}
              className="w-full px-3 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value={5}>5 videos</option>
              <option value={10}>10 videos</option>
              <option value={15}>15 videos</option>
              <option value={20}>20 videos</option>
            </select>
          </div>
        </div>

        <button
          onClick={handleSearch}
          disabled={isLoading || !searchQuery.trim()}
          className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
        >
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Searching Douyin...
            </>
          ) : (
            <>
              <Search className="w-4 h-4" />
              Search Videos
            </>
          )}
        </button>

        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            💡 <strong>Tip:</strong> Use Chinese keywords for better search
            results. The search will look for videos on Douyin (TikTok China)
            matching your query.
          </p>
        </div>
      </div>
    </div>
  );
}
