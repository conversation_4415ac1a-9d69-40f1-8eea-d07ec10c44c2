# Douyin Translator App

A modern, production-ready full-stack application that translates text to Chinese and searches for relevant Douyin (TikTok China) videos with download capabilities.

## 🌟 Features

### 🌐 Text Translation

- **Multi-language Support**: Translate from any language to Chinese
- **Auto-detection**: Automatically detect source language
- **Real-time Translation**: Fast and accurate translation with Google Translate API
- **Caching**: Intelligent caching for improved performance
- **Fallback System**: Mock translation service for development

### 🎥 Video Search

- **Douyin Integration**: Search for videos on Douyin using Chinese keywords
- **Smart Results**: Get video titles, durations, thumbnails, and author information
- **Customizable Limits**: Choose how many results to display (5-20 videos)
- **Caching**: Search results caching for better performance

### 📥 Video Download

- **One-click Download**: Download videos directly from Douyin
- **Quality Options**: Choose video quality (best, 720p, 480p, 360p)
- **Progress Tracking**: Real-time download status and progress
- **File Management**: Organized download storage with metadata

### 🎨 Modern UI/UX

- **Responsive Design**: Works perfectly on desktop and mobile
- **Dark Mode Support**: Automatic dark/light theme switching
- **Loading States**: Beautiful loading animations and progress indicators
- **Toast Notifications**: Real-time feedback for all operations

### 🔐 Production Features

- **Authentication**: JWT-based user authentication
- **Rate Limiting**: API rate limiting to prevent abuse
- **Caching**: Redis-based caching for improved performance
- **Security**: CORS, HTTPS, security headers
- **Monitoring**: Health checks and logging
- **Scalability**: Docker containerization and load balancing

## Tech Stack

### Frontend

- **Next.js 14** with TypeScript
- **Tailwind CSS** for styling
- **React Hot Toast** for notifications
- **Lucide React** for icons
- **Axios** for API communication

### Backend

- **FastAPI** with Python
- **Playwright** for web scraping
- **yt-dlp** for video downloading
- **Google Translate API** for translation
- **SQLite** for data storage
- **Uvicorn** ASGI server

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm
- Python 3.11+
- Docker and Docker Compose
- Redis (for production caching)

### Option 1: Local Development

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd douyin-translator-app
   ```

2. **Install dependencies**

   ```bash
   npm run install:all
   ```

3. **Set up environment variables**

   ```bash
   # Frontend (.env.local)
   NEXT_PUBLIC_API_URL=http://localhost:8000

   # Backend (.env)
   API_HOST=0.0.0.0
   API_PORT=8000
   DEBUG=True
   ```

4. **Install Python dependencies**

   ```bash
   cd backend
   pip install -r requirements.txt
   playwright install chromium
   ```

5. **Start the application**

   ```bash
   # From root directory
   npm run dev
   ```

   This will start:

   - Frontend: http://localhost:3000
   - Backend: http://localhost:8000

### Option 2: Docker

1. **Build and run with Docker Compose**

   ```bash
   docker-compose up --build
   ```

2. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs

### Option 3: One-Click Deployment

Use our deployment script for easy setup:

```bash
# Development environment
./deploy.sh development

# Production environment
./deploy.sh production yourdomain.com

# Staging environment
./deploy.sh staging staging.yourdomain.com
```

## 🏭 Production Deployment

### Prerequisites for Production

- Domain name with DNS configured
- SSL certificates (Let's Encrypt recommended)
- Server with Docker and Docker Compose
- Redis server
- PostgreSQL database (recommended)

### Production Setup

1. **Clone and configure**

   ```bash
   git clone <repository-url>
   cd douyin-translator-app
   cp backend/.env.production.example backend/.env.production
   ```

2. **Configure environment variables**
   Edit `backend/.env.production` with your production settings:

   ```env
   GOOGLE_TRANSLATE_API_KEY=your_production_api_key
   SECRET_KEY=your_super_secret_key
   DATABASE_URL=postgresql://user:pass@localhost:5432/douyin_translator
   REDIS_URL=redis://redis:6379/0
   ALLOWED_ORIGINS=https://yourdomain.com
   ```

3. **Set up SSL certificates**

   ```bash
   mkdir ssl
   # Add your SSL certificates or use Let's Encrypt
   ```

4. **Deploy**
   ```bash
   ./deploy.sh production yourdomain.com
   ```

### Production Features

- **SSL/HTTPS**: Automatic HTTPS redirect and security headers
- **Rate Limiting**: API rate limiting to prevent abuse
- **Caching**: Redis-based caching for improved performance
- **Authentication**: JWT-based user authentication
- **Monitoring**: Health checks and structured logging
- **Security**: CORS, input validation, and security headers
- **Scalability**: Multi-container architecture with load balancing

## API Endpoints

### Translation

- `POST /api/translate` - Translate text to Chinese
- `GET /api/health` - Health check

### Video Search

- `POST /api/search` - Search Douyin videos

### Video Download

- `POST /api/download` - Download a video
- `GET /api/download/{filename}` - Serve downloaded files

## Usage Guide

### 1. Text Translation

1. Enter text in any language in the input field
2. Select source language (or use auto-detect)
3. Click "Translate to Chinese" or press Ctrl+Enter
4. View the Chinese translation in the output area

### 2. Video Search

1. After translation, the Chinese text will automatically populate the search field
2. Adjust the number of results if needed (5-20 videos)
3. Click "Search Videos" to find Douyin content
4. Browse through video results with titles, authors, and durations

### 3. Video Download

1. Click the "Download" button on any video result
2. Wait for the download to complete
3. The file will be automatically downloaded to your device
4. Use "View Original" to open the video on Douyin

## Configuration

### Environment Variables

#### Frontend (.env.local)

```env
NEXT_PUBLIC_API_URL=http://localhost:8000
```

#### Backend (.env)

```env
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=True
DATABASE_URL=sqlite:///./app_data.db
GOOGLE_TRANSLATE_API_KEY=your_api_key_here
DOWNLOAD_DIR=downloads
MAX_DOWNLOAD_SIZE=500MB
SECRET_KEY=your-secret-key-here
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
```

## Development

### Project Structure

```
douyin-translator-app/
├── frontend/                 # Next.js React app
│   ├── src/
│   │   ├── app/             # Next.js app router
│   │   ├── components/      # React components
│   │   ├── types/           # TypeScript types
│   │   └── utils/           # Utility functions
│   ├── package.json
│   └── Dockerfile
├── backend/                 # FastAPI Python app
│   ├── app/
│   │   ├── services/        # Business logic
│   │   └── database.py      # Database operations
│   ├── downloads/           # Downloaded videos
│   ├── main.py             # FastAPI app
│   ├── requirements.txt
│   └── Dockerfile
├── docker-compose.yml
└── README.md
```

### Adding New Features

1. **Frontend Components**: Add new React components in `frontend/src/components/`
2. **API Endpoints**: Add new routes in `backend/main.py`
3. **Services**: Add business logic in `backend/app/services/`
4. **Types**: Define TypeScript interfaces in `frontend/src/types/`

## Troubleshooting

### Common Issues

1. **Translation not working**

   - Check if Google Translate API key is configured
   - Verify internet connection
   - Check backend logs for errors

2. **Video search failing**

   - Ensure Playwright is properly installed
   - Check if Douyin is accessible from your location
   - Verify Chinese text is properly formatted

3. **Download errors**
   - Check if yt-dlp is up to date
   - Verify video URL is accessible
   - Ensure sufficient disk space

### Logs and Debugging

- Frontend logs: Browser developer console
- Backend logs: Terminal where uvicorn is running
- Docker logs: `docker-compose logs -f`

## Security Considerations

- API keys should be kept secure and not committed to version control
- Rate limiting should be implemented for production use
- User input validation is implemented on both frontend and backend
- CORS is configured to allow only specified origins

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This application is for educational and personal use only. Please respect copyright laws and Douyin's terms of service when downloading content. Users are responsible for ensuring they have permission to download and use any content.
