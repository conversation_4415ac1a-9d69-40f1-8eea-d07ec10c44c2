"""Check if a host is in the Google Chrome HSTS Preload list"""

import functools
import os
import typing

__version__ = "2025.1.1"
__checksum__ = "2b5afe1338eff60488890dd0238d4e6c99f6ad42b23720d6fc64b916e12ba770"
__all__ = ["in_hsts_preload"]

# fmt: off
_GTLD_INCLUDE_SUBDOMAINS = {b'amazon', b'android', b'app', b'audible', b'azure', b'bank', b'bing', b'boo', b'channel', b'chrome', b'dad', b'day', b'dev', b'eat', b'esq', b'fire', b'fly', b'foo', b'fujitsu', b'gle', b'gmail', b'google', b'hangout', b'hotmail', b'imdb', b'ing', b'insurance', b'kindle', b'meet', b'meme', b'microsoft', b'mov', b'new', b'nexus', b'office', b'page', b'phd', b'play', b'prime', b'prof', b'rsvp', b'search', b'silk', b'skype', b'windows', b'xbox', b'xn--cckwcxetd', b'xn--jlq480n2rg', b'youtube', b'zappos', b'zip'}  # noqa: E501
_JUMPTABLE = [[(0, 11), (11, 5), (16, 19), (35, 55), (90, 26), (116, 12), (128, 8), (136, 19), (155, 22), (177, 7), (184, 20), (204, 18), None, (222, 39), (261, 37), (298, 19), (317, 9), (326, 36), (362, 24), (386, 10), (396, 18), None, (414, 62), (476, 8), (484, 22), (506, 19), (525, 13), (538, 14), (552, 21), None, None, (573, 40), (613, 20), (633, 25), (658, 22), (680, 22), (702, 28), None, (730, 25), (755, 25), (780, 8), (788, 13), (801, 10), (811, 8), (819, 17), (836, 6), (842, 19), (861, 16), (877, 5), (882, 19), (901, 14), (915, 11), (926, 18), (944, 27), (971, 7), (978, 11), (989, 15), (1004, 12), (1016, 20), (1036, 8), (1044, 27), (1071, 46), (1117, 25), (1142, 16), (1158, 8), (1166, 5), (1171, 22), (1193, 18), None, (1211, 36), (1247, 15), (1262, 24), (1286, 11), None, (1297, 10), (1307, 23), (1330, 18), (1348, 18), (1366, 5), (1371, 20), (1391, 26), (1417, 27), (1444, 28), (1472, 20), (1492, 59), (1551, 21), (1572, 14), (1586, 8), (1594, 8), (1602, 10), (1612, 13), (1625, 20), (1645, 19), None, (1664, 13), (1677, 26), (1703, 11), (1714, 4), (1718, 22), (1740, 19), (1759, 21), (1780, 14), (1794, 28), (1822, 11), (1833, 10), (1843, 12), (1855, 25), None, (1880, 19), (1899, 14), (1913, 26), (1939, 45), (1984, 15), None, (1999, 11), (2010, 30), (2040, 21), (2061, 26), (2087, 6), (2093, 6), (2099, 14), (2113, 5), (2118, 30), (2148, 23), (2171, 6), (2177, 17), (2194, 8), (2202, 19), (2221, 12), (2233, 70), (2303, 55), (2358, 12), (2370, 23), (2393, 16), (2409, 29), (2438, 9), (2447, 24), (2471, 44), (2515, 6), (2521, 41), (2562, 22), (2584, 23), (2607, 31), (2638, 20), (2658, 8), (2666, 15), (2681, 12), (2693, 32), (2725, 25), (2750, 15), None, (2765, 46), (2811, 21), (2832, 17), (2849, 14), (2863, 26), (2889, 5), (2894, 37), (2931, 39), (2970, 16), (2986, 33), (3019, 17), (3036, 23), (3059, 21), (3080, 25), (3105, 19), (3124, 14), (3138, 7), (3145, 37), None, (3182, 18), (3200, 21), (3221, 27), (3248, 17), (3265, 24), (3289, 12), (3301, 36), (3337, 35), (3372, 12), (3384, 57), (3441, 25), (3466, 32), None, (3498, 8), (3506, 25), (3531, 18), (3549, 6), (3555, 23), None, (3578, 36), (3614, 33), (3647, 14), (3661, 16), (3677, 22), None, (3699, 30), (3729, 41), (3770, 50), (3820, 15), (3835, 20), (3855, 31), (3886, 21), (3907, 32), (3939, 24), (3963, 20), (3983, 17), (4000, 60), (4060, 6), (4066, 9), (4075, 12), (4087, 18), (4105, 11), (4116, 10), (4126, 39), (4165, 42), None, (4207, 36), (4243, 17), None, (4260, 8), (4268, 8), (4276, 17), None, (4293, 25), (4318, 17), None, (4335, 21), (4356, 35), (4391, 21), (4412, 10), (4422, 48), (4470, 11), (4481, 37), (4518, 41), (4559, 23), (4582, 12), (4594, 14), (4608, 23), (4631, 29), (4660, 14), (4674, 8), (4682, 47), (4729, 52), None, None, (4781, 47), (4828, 42), None, (4870, 18), None, (4888, 20), (4908, 8), (4916, 21), (4937, 6), (4943, 29), (4972, 22)], [(4994, 9577), (14571, 9062), (23633, 9808), (33441, 9464), (42905, 8995), (51900, 9396), (61296, 9877), (71173, 9009), (80182, 9064), (89246, 8973), (98219, 9449), (107668, 8833), (116501, 9305), (125806, 10272), (136078, 9198), (145276, 10055), (155331, 9928), (165259, 8832), (174091, 9599), (183690, 8800), (192490, 9873), (202363, 9109), (211472, 9625), (221097, 9634), (230731, 9840), (240571, 9135), (249706, 9824), (259530, 10373), (269903, 9214), (279117, 8953), (288070, 9872), (297942, 9474), (307416, 9340), (316756, 9660), (326416, 9175), (335591, 9767), (345358, 9515), (354873, 10563), (365436, 9958), (375394, 9793), (385187, 10162), (395349, 8381), (403730, 9470), (413200, 9240), (422440, 8976), (431416, 9530), (440946, 8904), (449850, 10613), (460463, 9115), (469578, 8738), (478316, 9555), (487871, 9035), (496906, 9375), (506281, 9333), (515614, 8985), (524599, 9284), (533883, 9281), (543164, 9501), (552665, 9575), (562240, 8345), (570585, 8785), (579370, 9736), (589106, 9612), (598718, 9914), (608632, 10382), (619014, 8935), (627949, 9415), (637364, 9973), (647337, 10238), (657575, 9247), (666822, 8845), (675667, 9135), (684802, 8534), (693336, 10159), (703495, 9227), (712722, 9777), (722499, 8817), (731316, 10623), (741939, 9545), (751484, 8946), (760430, 9539), (769969, 8794), (778763, 8972), (787735, 9421), (797156, 9517), (806673, 9699), (816372, 10216), (826588, 9197), (835785, 8774), (844559, 9399), (853958, 10582), (864540, 9306), (873846, 8758), (882604, 9168), (891772, 9389), (901161, 9230), (910391, 9379), (919770, 8665), (928435, 9381), (937816, 8658), (946474, 9161), (955635, 8963), (964598, 9330), (973928, 8960), (982888, 9175), (992063, 10146), (1002209, 9552), (1011761, 10008), (1021769, 9105), (1030874, 9047), (1039921, 9892), (1049813, 9673), (1059486, 8496), (1067982, 8785), (1076767, 9390), (1086157, 9150), (1095307, 9703), (1105010, 9750), (1114760, 9375), (1124135, 9212), (1133347, 9515), (1142862, 9244), (1152106, 9189), (1161295, 9351), (1170646, 9623), (1180269, 9120), (1189389, 9484), (1198873, 10161), (1209034, 9102), (1218136, 10694), (1228830, 10359), (1239189, 9364), (1248553, 8822), (1257375, 8586), (1265961, 8861), (1274822, 10181), (1285003, 10155), (1295158, 10483), (1305641, 9504), (1315145, 10096), (1325241, 10331), (1335572, 9822), (1345394, 9366), (1354760, 9478), (1364238, 10575), (1374813, 8140), (1382953, 9641), (1392594, 9396), (1401990, 9237), (1411227, 9371), (1420598, 9147), (1429745, 9246), (1438991, 9145), (1448136, 8771), (1456907, 9025), (1465932, 9526), (1475458, 9709), (1485167, 9658), (1494825, 9009), (1503834, 8800), (1512634, 8899), (1521533, 9351), (1530884, 9509), (1540393, 9049), (1549442, 9461), (1558903, 10479), (1569382, 10295), (1579677, 8695), (1588372, 10731), (1599103, 9366), (1608469, 9298), (1617767, 9232), (1626999, 9872), (1636871, 9154), (1646025, 9137), (1655162, 9562), (1664724, 10106), (1674830, 9671), (1684501, 9192), (1693693, 8766), (1702459, 11721), (1714180, 10098), (1724278, 8875), (1733153, 9124), (1742277, 9591), (1751868, 9839), (1761707, 8899), (1770606, 10254), (1780860, 9299), (1790159, 9590), (1799749, 9493), (1809242, 9229), (1818471, 9291), (1827762, 8527), (1836289, 9211), (1845500, 8777), (1854277, 8643), (1862920, 8929), (1871849, 9409), (1881258, 9766), (1891024, 9447), (1900471, 9731), (1910202, 9618), (1919820, 8367), (1928187, 9683), (1937870, 9917), (1947787, 8832), (1956619, 9512), (1966131, 10587), (1976718, 8939), (1985657, 10155), (1995812, 9558), (2005370, 9078), (2014448, 9104), (2023552, 9648), (2033200, 9427), (2042627, 9002), (2051629, 9354), (2060983, 9189), (2070172, 10067), (2080239, 9099), (2089338, 8869), (2098207, 9681), (2107888, 9347), (2117235, 9663), (2126898, 9080), (2135978, 10068), (2146046, 9741), (2155787, 9569), (2165356, 10065), (2175421, 10027), (2185448, 9346), (2194794, 9037), (2203831, 10063), (2213894, 8886), (2222780, 10337), (2233117, 9079), (2242196, 8668), (2250864, 8674), (2259538, 9894), (2269432, 9877), (2279309, 9574), (2288883, 9671), (2298554, 9843), (2308397, 9040), (2317437, 10514), (2327951, 9992), (2337943, 9197), (2347140, 9618), (2356758, 8760), (2365518, 9279), (2374797, 9365), (2384162, 9076), (2393238, 8978), (2402216, 9344), (2411560, 9598)], [(2421158, 963), (2422121, 854), (2422975, 928), (2423903, 857), (2424760, 941), (2425701, 840), (2426541, 683), (2427224, 969), (2428193, 685), (2428878, 957), (2429835, 529), (2430364, 755), (2431119, 981), (2432100, 1097), (2433197, 833), (2434030, 773), (2434803, 1306), (2436109, 829), (2436938, 814), (2437752, 848), (2438600, 1198), (2439798, 791), (2440589, 797), (2441386, 740), (2442126, 809), (2442935, 836), (2443771, 953), (2444724, 1371), (2446095, 868), (2446963, 790), (2447753, 878), (2448631, 784), (2449415, 754), (2450169, 831), (2451000, 1078), (2452078, 1122), (2453200, 622), (2453822, 934), (2454756, 779), (2455535, 1035), (2456570, 872), (2457442, 804), (2458246, 745), (2458991, 880), (2459871, 852), (2460723, 874), (2461597, 807), (2462404, 868), (2463272, 841), (2464113, 618), (2464731, 780), (2465511, 801), (2466312, 861), (2467173, 980), (2468153, 1474), (2469627, 692), (2470319, 913), (2471232, 770), (2472002, 691), (2472693, 810), (2473503, 896), (2474399, 888), (2475287, 1077), (2476364, 932), (2477296, 877), (2478173, 844), (2479017, 1034), (2480051, 727), (2480778, 591), (2481369, 1082), (2482451, 888), (2483339, 757), (2484096, 450), (2484546, 843), (2485389, 454), (2485843, 827), (2486670, 656), (2487326, 1003), (2488329, 944), (2489273, 654), (2489927, 910), (2490837, 831), (2491668, 956), (2492624, 802), (2493426, 1029), (2494455, 968), (2495423, 772), (2496195, 608), (2496803, 934), (2497737, 960), (2498697, 1152), (2499849, 1204), (2501053, 1011), (2502064, 809), (2502873, 728), (2503601, 638), (2504239, 1015), (2505254, 863), (2506117, 774), (2506891, 786), (2507677, 863), (2508540, 817), (2509357, 833), (2510190, 624), (2510814, 830), (2511644, 976), (2512620, 757), (2513377, 622), (2513999, 882), (2514881, 1067), (2515948, 896), (2516844, 1058), (2517902, 1124), (2519026, 860), (2519886, 739), (2520625, 782), (2521407, 821), (2522228, 905), (2523133, 676), (2523809, 771), (2524580, 1106), (2525686, 782), (2526468, 950), (2527418, 865), (2528283, 956), (2529239, 894), (2530133, 876), (2531009, 939), (2531948, 654), (2532602, 995), (2533597, 799), (2534396, 1037), (2535433, 758), (2536191, 683), (2536874, 684), (2537558, 998), (2538556, 990), (2539546, 724), (2540270, 671), (2540941, 941), (2541882, 863), (2542745, 716), (2543461, 773), (2544234, 697), (2544931, 838), (2545769, 708), (2546477, 878), (2547355, 669), (2548024, 895), (2548919, 745), (2549664, 883), (2550547, 728), (2551275, 825), (2552100, 947), (2553047, 1095), (2554142, 925), (2555067, 837), (2555904, 1106), (2557010, 934), (2557944, 785), (2558729, 1115), (2559844, 742), (2560586, 1001), (2561587, 982), (2562569, 1156), (2563725, 1189), (2564914, 686), (2565600, 970), (2566570, 746), (2567316, 749), (2568065, 884), (2568949, 540), (2569489, 1225), (2570714, 695), (2571409, 1035), (2572444, 868), (2573312, 1050), (2574362, 871), (2575233, 685), (2575918, 790), (2576708, 2052), (2578760, 873), (2579633, 940), (2580573, 842), (2581415, 1093), (2582508, 652), (2583160, 966), (2584126, 953), (2585079, 648), (2585727, 1060), (2586787, 644), (2587431, 630), (2588061, 562), (2588623, 1264), (2589887, 835), (2590722, 794), (2591516, 788), (2592304, 730), (2593034, 1019), (2594053, 981), (2595034, 944), (2595978, 889), (2596867, 973), (2597840, 785), (2598625, 781), (2599406, 767), (2600173, 805), (2600978, 1051), (2602029, 1008), (2603037, 824), (2603861, 856), (2604717, 756), (2605473, 925), (2606398, 1150), (2607548, 573), (2608121, 830), (2608951, 798), (2609749, 863), (2610612, 742), (2611354, 646), (2612000, 675), (2612675, 945), (2613620, 938), (2614558, 1073), (2615631, 580), (2616211, 749), (2616960, 769), (2617729, 878), (2618607, 856), (2619463, 833), (2620296, 1061), (2621357, 606), (2621963, 1095), (2623058, 760), (2623818, 748), (2624566, 824), (2625390, 922), (2626312, 720), (2627032, 974), (2628006, 970), (2628976, 884), (2629860, 550), (2630410, 936), (2631346, 717), (2632063, 993), (2633056, 576), (2633632, 806), (2634438, 926), (2635364, 752), (2636116, 978), (2637094, 2403), (2639497, 763), (2640260, 997), (2641257, 816), (2642073, 928), (2643001, 831)], [(2643832, 48), None, None, (2643880, 42), None, (2643922, 27), (2643949, 43), None, None, None, None, None, None, None, (2643992, 21), None, None, (2644013, 42), (2644055, 20), (2644075, 25), (2644100, 44), None, (2644144, 64), (2644208, 18), None, (2644226, 23), None, None, None, None, None, None, (2644249, 21), (2644270, 46), None, None, (2644316, 44), None, None, None, None, (2644360, 44), (2644404, 42), (2644446, 23), None, None, None, None, (2644469, 42), None, None, None, None, None, (2644511, 31), None, None, None, None, (2644542, 42), (2644584, 19), None, None, (2644603, 21), None, (2644624, 26), (2644650, 56), None, (2644706, 16), (2644722, 42), (2644764, 27), None, None, None, None, (2644791, 21), (2644812, 52), None, (2644864, 17), (2644881, 20), (2644901, 42), None, None, None, (2644943, 40), (2644983, 17), (2645000, 27), (2645027, 21), None, None, None, (2645048, 24), None, (2645072, 24), (2645096, 21), (2645117, 24), None, (2645141, 48), None, None, (2645189, 17), (2645206, 19), None, None, None, None, (2645225, 31), None, None, None, None, None, None, (2645256, 28), None, (2645284, 42), (2645326, 42), (2645368, 17), (2645385, 17), (2645402, 26), (2645428, 17), (2645445, 19), None, (2645464, 25), (2645489, 17), None, (2645506, 20), None, None, (2645526, 42), (2645568, 63), (2645631, 17), None, None, (2645648, 21), None, None, None, None, (2645669, 21), (2645690, 16), None, (2645706, 31), None, None, None, None, (2645737, 42), None, (2645779, 80), None, (2645859, 9), None, (2645868, 21), (2645889, 42), None, (2645931, 18), (2645949, 90), (2646039, 56), (2646095, 45), None, (2646140, 61), None, None, (2646201, 24), (2646225, 21), None, None, None, None, None, (2646246, 42), (2646288, 21), (2646309, 21), None, (2646330, 42), (2646372, 25), None, (2646397, 55), (2646452, 21), (2646473, 42), None, None, (2646515, 36), (2646551, 16), None, None, (2646567, 16), None, (2646583, 35), None, None, (2646618, 21), None, (2646639, 22), (2646661, 21), (2646682, 21), (2646703, 21), None, (2646724, 63), (2646787, 41), (2646828, 39), (2646867, 42), None, None, None, None, None, (2646909, 15), (2646924, 21), (2646945, 21), None, None, (2646966, 21), None, (2646987, 23), (2647010, 21), None, None, (2647031, 19), (2647050, 66), None, None, None, (2647116, 50), None, (2647166, 21), (2647187, 21), (2647208, 19), None, (2647227, 16), (2647243, 26), None, (2647269, 58), (2647327, 42), None, None, None, None, None, None, (2647369, 21), None, None, None, (2647390, 21), None, None, (2647411, 64), None, (2647475, 254), (2647729, 21), (2647750, 24), None, (2647774, 21), (2647795, 29)], [None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, (2647824, 62), None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None, None]]  # noqa: E501
_CRC8_TABLE = [
    0x00, 0x07, 0x0e, 0x09, 0x1c, 0x1b, 0x12, 0x15,
    0x38, 0x3f, 0x36, 0x31, 0x24, 0x23, 0x2a, 0x2d,
    0x70, 0x77, 0x7e, 0x79, 0x6c, 0x6b, 0x62, 0x65,
    0x48, 0x4f, 0x46, 0x41, 0x54, 0x53, 0x5a, 0x5d,
    0xe0, 0xe7, 0xee, 0xe9, 0xfc, 0xfb, 0xf2, 0xf5,
    0xd8, 0xdf, 0xd6, 0xd1, 0xc4, 0xc3, 0xca, 0xcd,
    0x90, 0x97, 0x9e, 0x99, 0x8c, 0x8b, 0x82, 0x85,
    0xa8, 0xaf, 0xa6, 0xa1, 0xb4, 0xb3, 0xba, 0xbd,
    0xc7, 0xc0, 0xc9, 0xce, 0xdb, 0xdc, 0xd5, 0xd2,
    0xff, 0xf8, 0xf1, 0xf6, 0xe3, 0xe4, 0xed, 0xea,
    0xb7, 0xb0, 0xb9, 0xbe, 0xab, 0xac, 0xa5, 0xa2,
    0x8f, 0x88, 0x81, 0x86, 0x93, 0x94, 0x9d, 0x9a,
    0x27, 0x20, 0x29, 0x2e, 0x3b, 0x3c, 0x35, 0x32,
    0x1f, 0x18, 0x11, 0x16, 0x03, 0x04, 0x0d, 0x0a,
    0x57, 0x50, 0x59, 0x5e, 0x4b, 0x4c, 0x45, 0x42,
    0x6f, 0x68, 0x61, 0x66, 0x73, 0x74, 0x7d, 0x7a,
    0x89, 0x8e, 0x87, 0x80, 0x95, 0x92, 0x9b, 0x9c,
    0xb1, 0xb6, 0xbf, 0xb8, 0xad, 0xaa, 0xa3, 0xa4,
    0xf9, 0xfe, 0xf7, 0xf0, 0xe5, 0xe2, 0xeb, 0xec,
    0xc1, 0xc6, 0xcf, 0xc8, 0xdd, 0xda, 0xd3, 0xd4,
    0x69, 0x6e, 0x67, 0x60, 0x75, 0x72, 0x7b, 0x7c,
    0x51, 0x56, 0x5f, 0x58, 0x4d, 0x4a, 0x43, 0x44,
    0x19, 0x1e, 0x17, 0x10, 0x05, 0x02, 0x0b, 0x0c,
    0x21, 0x26, 0x2f, 0x28, 0x3d, 0x3a, 0x33, 0x34,
    0x4e, 0x49, 0x40, 0x47, 0x52, 0x55, 0x5c, 0x5b,
    0x76, 0x71, 0x78, 0x7f, 0x6a, 0x6d, 0x64, 0x63,
    0x3e, 0x39, 0x30, 0x37, 0x22, 0x25, 0x2c, 0x2b,
    0x06, 0x01, 0x08, 0x0f, 0x1a, 0x1d, 0x14, 0x13,
    0xae, 0xa9, 0xa0, 0xa7, 0xb2, 0xb5, 0xbc, 0xbb,
    0x96, 0x91, 0x98, 0x9f, 0x8a, 0x8d, 0x84, 0x83,
    0xde, 0xd9, 0xd0, 0xd7, 0xc2, 0xc5, 0xcc, 0xcb,
    0xe6, 0xe1, 0xe8, 0xef, 0xfa, 0xfd, 0xf4, 0xf3
]
# fmt: on

_IS_LEAF = 0x80
_INCLUDE_SUBDOMAINS = 0x40


try:
    from importlib.resources import open_binary

    def open_pkg_binary(path: str) -> typing.BinaryIO:
        return open_binary("hstspreload", path)


except ImportError:

    def open_pkg_binary(path: str) -> typing.BinaryIO:
        return open(
            os.path.join(os.path.dirname(os.path.abspath(__file__)), path),
            "rb",
        )


@functools.lru_cache(maxsize=1024)
def in_hsts_preload(host: typing.AnyStr) -> bool:
    """Determines if an IDNA-encoded host is on the HSTS preload list"""

    if isinstance(host, str):
        host = host.encode("ascii")
    labels = host.lower().split(b".")

    # Fast-branch for gTLDs that are registered to preload all sub-domains.
    if labels[-1] in _GTLD_INCLUDE_SUBDOMAINS:
        return True

    with open_pkg_binary("hstspreload.bin") as f:
        for layer, label in enumerate(labels[::-1]):
            # None of our layers are greater than 5 deep.
            if layer > 4:
                return False

            # Read the jump table for the layer and label
            jump_info = _JUMPTABLE[layer][_crc8(label)]
            if jump_info is None:
                # No entry: host is not preloaded
                return False

            # Read the set of entries for that layer and label
            f.seek(jump_info[0])
            data = bytearray(jump_info[1])
            f.readinto(data)

            for is_leaf, include_subdomains, ent_label in _iter_entries(data):
                # We found a potential leaf
                if is_leaf:
                    if ent_label == host:
                        return True
                    if include_subdomains and host.endswith(b"." + ent_label):
                        return True

                # Continue traversing as we're not at a leaf.
                elif label == ent_label:
                    break
            else:
                return False
    return False


def _iter_entries(data: bytes) -> typing.Iterable[typing.Tuple[int, int, bytes]]:
    while data:
        flags = data[0]
        size = data[1]
        label = bytes(data[2 : 2 + size])
        yield (flags & _IS_LEAF, flags & _INCLUDE_SUBDOMAINS, label)
        data = data[2 + size :]


def _crc8(value: bytes) -> int:
    # CRC8 reference implementation: https://github.com/niccokunzmann/crc8
    checksum = 0x00
    for byte in value:
        checksum = _CRC8_TABLE[checksum ^ byte]
    return checksum
