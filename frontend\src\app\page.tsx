"use client";

import { useState } from "react";
import { Toaster } from "react-hot-toast";
import TranslationSection from "@/components/TranslationSection";
import SearchSection from "@/components/SearchSection";
import VideoResults from "@/components/VideoResults";
import { VideoInfo } from "@/types";

export default function Home() {
  const [translatedText, setTranslatedText] = useState<string>("");
  const [videos, setVideos] = useState<VideoInfo[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-slate-900 dark:to-gray-800">
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: "#363636",
            color: "#fff",
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: "#10b981",
              secondary: "#fff",
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: "#ef4444",
              secondary: "#fff",
            },
          },
        }}
      />

      <div className="container mx-auto px-4 py-6 md:py-12">
        {/* Header */}
        <header className="text-center mb-8 md:mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl mb-6">
            <span className="text-2xl font-bold text-white">抖</span>
          </div>
          <h1 className="text-3xl md:text-5xl lg:text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent mb-4">
            Douyin Translator
          </h1>
          <p className="text-base md:text-lg text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
            🚀 Translate any text to Chinese and discover amazing Douyin videos.
            <br />
            📥 Download your favorites with just one click! Fast, reliable, and
            easy to use.
          </p>

          {/* Status Indicators */}
          <div className="flex flex-wrap justify-center gap-3 mt-6">
            <div className="inline-flex items-center gap-2 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-3 py-1 rounded-full text-sm font-medium">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              Translation Active
            </div>
            <div className="inline-flex items-center gap-2 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 px-3 py-1 rounded-full text-sm font-medium">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              Search Ready
            </div>
            <div className="inline-flex items-center gap-2 bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 px-3 py-1 rounded-full text-sm font-medium">
              <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
              Download Ready
            </div>
          </div>
        </header>

        {/* Progress Indicator */}
        {isLoading && (
          <div className="fixed top-0 left-0 w-full h-1 bg-gray-200 dark:bg-gray-700 z-50">
            <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 animate-pulse"></div>
          </div>
        )}

        {/* Main Content */}
        <div className="max-w-6xl mx-auto space-y-6 md:space-y-8">
          {/* Translation Section */}
          <div className="transform transition-all duration-300 hover:scale-[1.01]">
            <TranslationSection
              onTranslationComplete={setTranslatedText}
              isLoading={isLoading}
              setIsLoading={setIsLoading}
            />
          </div>

          {/* Search Section */}
          {translatedText && (
            <div className="transform transition-all duration-300 hover:scale-[1.01]">
              <SearchSection
                translatedText={translatedText}
                onSearchComplete={setVideos}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
              />
            </div>
          )}

          {/* Video Results */}
          {videos.length > 0 && (
            <div className="transform transition-all duration-300">
              <VideoResults
                videos={videos}
                isLoading={isLoading}
                setIsLoading={setIsLoading}
              />
            </div>
          )}

          {/* Quick Start Guide */}
          {!translatedText && !isLoading && (
            <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 dark:border-gray-700/50 p-6 md:p-8">
              <h3 className="text-xl font-semibold text-gray-800 dark:text-white mb-4 text-center">
                🚀 Quick Start Guide
              </h3>
              <div className="grid md:grid-cols-3 gap-4 text-center">
                <div className="space-y-2">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-xl">1️⃣</span>
                  </div>
                  <h4 className="font-medium text-gray-800 dark:text-white">
                    Translate
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Enter English text and get instant Chinese translation
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-xl">2️⃣</span>
                  </div>
                  <h4 className="font-medium text-gray-800 dark:text-white">
                    Search
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Find relevant Douyin videos using Chinese keywords
                  </p>
                </div>
                <div className="space-y-2">
                  <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto">
                    <span className="text-xl">3️⃣</span>
                  </div>
                  <h4 className="font-medium text-gray-800 dark:text-white">
                    Download
                  </h4>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Download videos directly to your device
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Enhanced Footer */}
        <footer className="mt-16 md:mt-24 text-center">
          <div className="bg-white/30 dark:bg-gray-800/30 backdrop-blur-sm rounded-2xl border border-gray-200/50 dark:border-gray-700/50 p-6 max-w-4xl mx-auto">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="text-gray-600 dark:text-gray-400">
                <p className="font-medium">Douyin Translator App</p>
                <p className="text-sm">Built with ❤️ using Next.js & FastAPI</p>
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                <span className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  API Online
                </span>
                <span>•</span>
                <span>v1.0.0</span>
                <span>•</span>
                <span>&copy; 2024</span>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}
