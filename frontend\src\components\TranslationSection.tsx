"use client";

import { useState } from "react";
import { toast } from "react-hot-toast";
import { Languages, ArrowRight, Loader2 } from "lucide-react";
import { translateText } from "@/utils/api";

interface TranslationSectionProps {
  onTranslationComplete: (translatedText: string) => void;
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

export default function TranslationSection({
  onTranslationComplete,
  isLoading,
  setIsLoading,
}: TranslationSectionProps) {
  const [inputText, setInputText] = useState<string>("");
  const [translatedText, setTranslatedText] = useState<string>("");
  const [sourceLanguage, setSourceLanguage] = useState<string>("auto");
  const [showAdvanced, setShowAdvanced] = useState<boolean>(false);
  const [translationHistory, setTranslationHistory] = useState<
    { input: string; output: string; timestamp: string }[]
  >([]);

  const handleTranslate = async () => {
    if (!inputText.trim()) {
      toast.error("Please enter some text to translate");
      return;
    }

    setIsLoading(true);
    try {
      const result = await translateText({
        text: inputText,
        source_language: sourceLanguage,
      });

      setTranslatedText(result.translated_text);
      onTranslationComplete(result.translated_text);

      // Add to history
      const newEntry = {
        input: inputText,
        output: result.translated_text,
        timestamp: new Date().toLocaleString(),
      };
      setTranslationHistory((prev) => [newEntry, ...prev.slice(0, 4)]); // Keep last 5

      toast.success(`Translated from ${result.source_language} to Chinese!`);
    } catch (error) {
      console.error("Translation error:", error);
      toast.error(
        error instanceof Error ? error.message : "Translation failed"
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && e.ctrlKey) {
      handleTranslate();
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 md:p-8">
      <div className="flex items-center gap-3 mb-6">
        <Languages className="w-6 h-6 text-blue-600 dark:text-blue-400" />
        <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
          Text Translation
        </h2>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {/* Input Section */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Source Language
            </label>
            <select
              value={sourceLanguage}
              onChange={(e) => setSourceLanguage(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
            >
              <option value="auto">Auto-detect</option>
              <option value="en">English</option>
              <option value="ja">Japanese</option>
              <option value="ko">Korean</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
              <option value="it">Italian</option>
              <option value="pt">Portuguese</option>
              <option value="ru">Russian</option>
              <option value="ar">Arabic</option>
              <option value="hi">Hindi</option>
              <option value="th">Thai</option>
              <option value="vi">Vietnamese</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Text to Translate
            </label>
            <textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter text in any language..."
              rows={6}
              className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 resize-none"
            />
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Press Ctrl+Enter to translate
            </p>
          </div>

          <button
            onClick={handleTranslate}
            disabled={isLoading || !inputText.trim()}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Translating...
              </>
            ) : (
              <>
                <Languages className="w-4 h-4" />
                Translate to Chinese
                <ArrowRight className="w-4 h-4" />
              </>
            )}
          </button>
        </div>

        {/* Output Section */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Chinese Translation
            </label>
            <div className="w-full h-[200px] px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white overflow-y-auto">
              {translatedText ? (
                <p className="whitespace-pre-wrap">{translatedText}</p>
              ) : (
                <p className="text-gray-500 dark:text-gray-400 italic">
                  Translation will appear here...
                </p>
              )}
            </div>
          </div>

          {translatedText && (
            <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
              <p className="text-sm text-green-800 dark:text-green-200">
                ✅ Translation complete! You can now search for Douyin videos
                using this Chinese text.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
