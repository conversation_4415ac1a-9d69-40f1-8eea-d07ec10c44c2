#!/bin/bash

# Douyin Translator App Deployment Script
# This script handles both development and production deployments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-development}
DOMAIN=${2:-localhost}

echo -e "${BLUE}🚀 Deploying Douyin Translator App${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"
echo -e "${BLUE}Domain: ${DOMAIN}${NC}"
echo ""

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_status "Docker and Docker Compose are available"

# Development deployment
if [ "$ENVIRONMENT" = "development" ]; then
    echo -e "${BLUE}📦 Starting development environment...${NC}"
    
    # Stop existing containers
    docker-compose down
    
    # Build and start containers
    docker-compose up --build -d
    
    print_status "Development environment started"
    echo ""
    echo -e "${GREEN}🌐 Application URLs:${NC}"
    echo -e "Frontend: http://localhost:3000"
    echo -e "Backend API: http://localhost:8000"
    echo -e "API Documentation: http://localhost:8000/docs"
    echo -e "Redis: localhost:6379"
    echo ""
    echo -e "${YELLOW}📝 To view logs:${NC}"
    echo "docker-compose logs -f"
    echo ""
    echo -e "${YELLOW}🛑 To stop:${NC}"
    echo "docker-compose down"

# Production deployment
elif [ "$ENVIRONMENT" = "production" ]; then
    echo -e "${BLUE}🏭 Starting production deployment...${NC}"
    
    # Check if production environment file exists
    if [ ! -f "backend/.env.production" ]; then
        print_error "Production environment file not found: backend/.env.production"
        print_warning "Please create the production environment file with your settings"
        exit 1
    fi
    
    # Update domain in configuration files
    if [ "$DOMAIN" != "localhost" ]; then
        echo -e "${BLUE}🔧 Updating domain configuration...${NC}"
        
        # Update Nginx configuration
        sed -i.bak "s/your-domain.com/$DOMAIN/g" nginx.prod.conf
        
        # Update Docker Compose
        sed -i.bak "s/your-domain.com/$DOMAIN/g" docker-compose.prod.yml
        
        print_status "Domain configuration updated"
    fi
    
    # Stop existing containers
    docker-compose -f docker-compose.prod.yml down
    
    # Build and start production containers
    docker-compose -f docker-compose.prod.yml up --build -d
    
    print_status "Production environment started"
    echo ""
    echo -e "${GREEN}🌐 Application URLs:${NC}"
    echo -e "Frontend: https://$DOMAIN"
    echo -e "Backend API: https://api.$DOMAIN"
    echo -e "API Documentation: https://api.$DOMAIN/docs"
    echo ""
    echo -e "${YELLOW}📝 To view logs:${NC}"
    echo "docker-compose -f docker-compose.prod.yml logs -f"
    echo ""
    echo -e "${YELLOW}🛑 To stop:${NC}"
    echo "docker-compose -f docker-compose.prod.yml down"
    echo ""
    echo -e "${YELLOW}🔒 SSL Certificate:${NC}"
    echo "Make sure to set up SSL certificates in the ./ssl directory"
    echo "You can use Let's Encrypt with the included certbot service"

# Staging deployment
elif [ "$ENVIRONMENT" = "staging" ]; then
    echo -e "${BLUE}🧪 Starting staging environment...${NC}"
    
    # Use production compose but with staging settings
    export ENVIRONMENT=staging
    docker-compose -f docker-compose.prod.yml up --build -d
    
    print_status "Staging environment started"

else
    print_error "Invalid environment: $ENVIRONMENT"
    echo "Usage: $0 [development|production|staging] [domain]"
    echo ""
    echo "Examples:"
    echo "  $0 development"
    echo "  $0 production yourdomain.com"
    echo "  $0 staging staging.yourdomain.com"
    exit 1
fi

echo ""
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"

# Health check
echo -e "${BLUE}🔍 Performing health check...${NC}"
sleep 10

if [ "$ENVIRONMENT" = "development" ]; then
    HEALTH_URL="http://localhost:8000/api/health"
else
    HEALTH_URL="https://api.$DOMAIN/api/health"
fi

if curl -f -s "$HEALTH_URL" > /dev/null; then
    print_status "Health check passed"
else
    print_warning "Health check failed - the application might still be starting up"
    echo "You can check the logs with: docker-compose logs -f"
fi

echo ""
echo -e "${BLUE}📚 Next steps:${NC}"
if [ "$ENVIRONMENT" = "production" ]; then
    echo "1. Set up SSL certificates"
    echo "2. Configure your domain DNS"
    echo "3. Set up monitoring and logging"
    echo "4. Configure backup procedures"
else
    echo "1. Open http://localhost:3000 in your browser"
    echo "2. Test the translation and search features"
    echo "3. Check the API documentation at http://localhost:8000/docs"
fi
