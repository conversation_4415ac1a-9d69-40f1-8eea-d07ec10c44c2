# Production Environment Configuration

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=False
ENVIRONMENT=production

# Database (Use PostgreSQL in production)
DATABASE_URL=postgresql://user:password@localhost:5432/douyin_translator

# External APIs
GOOGLE_TRANSLATE_API_KEY=your_production_google_translate_api_key

# Download settings
DOWNLOAD_DIR=/app/downloads
MAX_DOWNLOAD_SIZE=1GB

# Security (IMPORTANT: Change these in production!)
SECRET_KEY=your-super-secret-key-generated-with-openssl-rand-hex-32
ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=30
RATE_LIMIT_BURST=5

# Caching (Use Redis in production)
CACHE_TTL_TRANSLATION=7200
CACHE_TTL_SEARCH=3600
REDIS_URL=redis://redis:6379/0

# Logging
LOG_LEVEL=WARNING
LOG_FORMAT=json

# SSL/TLS
SSL_CERT_PATH=/etc/ssl/certs/cert.pem
SSL_KEY_PATH=/etc/ssl/private/key.pem

# Monitoring
SENTRY_DSN=your_sentry_dsn_here
ENABLE_METRICS=true

# Performance
WORKERS=4
MAX_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=5
