from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.security import HTT<PERSON><PERSON>earer
from pydantic import BaseModel, EmailStr
import os
import uvicorn
from typing import List, Optional, Dict, Any
import asyncio
from datetime import timedelta

# Import our services
from app.services.translation_service import TranslationService
from app.services.douyin_service import DouyinService
from app.services.download_service import DownloadService
from app.database import init_db
from app.auth import (
    auth_service,
    get_current_user,
    get_current_user_optional,
    authenticate_user,
    ACCESS_TOKEN_EXPIRE_MINUTES
)
from app.middleware import rate_limit_middleware, CacheService

app = FastAPI(
    title="Douyin Translator App",
    description="Modern app for text translation and Douyin video search/download",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://127.0.0.1:3000",
        "https://your-domain.com",  # Add your production domain
        "https://www.your-domain.com"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Rate limiting middleware disabled for testing
# app.middleware("http")(rate_limit_middleware)

# Initialize services
translation_service = TranslationService()
douyin_service = DouyinService()
download_service = DownloadService()

# Pydantic models
class TranslateRequest(BaseModel):
    text: str
    source_language: Optional[str] = "auto"

class TranslateResponse(BaseModel):
    original_text: str
    translated_text: str
    source_language: str
    target_language: str = "zh"

class SearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 10

class VideoInfo(BaseModel):
    title: str
    url: str
    duration: Optional[str] = None
    thumbnail: Optional[str] = None
    author: Optional[str] = None

class SearchResponse(BaseModel):
    query: str
    videos: List[VideoInfo]

class DownloadRequest(BaseModel):
    video_url: str
    quality: Optional[str] = "best"

class DownloadResponse(BaseModel):
    success: bool
    message: str
    file_path: Optional[str] = None
    file_size: Optional[str] = None

# Authentication models
class LoginRequest(BaseModel):
    email: str
    password: str

class LoginResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int

class UserResponse(BaseModel):
    id: str
    email: str
    is_active: bool
    is_premium: bool

@app.on_event("startup")
async def startup_event():
    """Initialize database and services on startup"""
    try:
        await init_db()
        await douyin_service.initialize()
    except Exception as e:
        print(f"Startup warning: {e}")
        # Continue anyway

@app.get("/")
async def root():
    return {"message": "Douyin Translator App API", "version": "1.0.0"}

# Authentication endpoints
@app.post("/api/auth/login", response_model=LoginResponse)
async def login(request: LoginRequest):
    """Login endpoint"""
    user = authenticate_user(request.email, request.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = auth_service.create_access_token(
        data={"sub": user["email"], "user_id": user["id"]},
        expires_delta=access_token_expires
    )

    return LoginResponse(
        access_token=access_token,
        expires_in=ACCESS_TOKEN_EXPIRE_MINUTES * 60
    )

@app.get("/api/auth/me", response_model=UserResponse)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """Get current user information"""
    from app.auth import get_user_by_email
    user = get_user_by_email(current_user["sub"])
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    return UserResponse(
        id=user["id"],
        email=user["email"],
        is_active=user["is_active"],
        is_premium=user["is_premium"]
    )

@app.post("/api/translate", response_model=TranslateResponse)
async def translate_text(
    request: TranslateRequest,
    current_user: Optional[dict] = Depends(get_current_user_optional)
):
    """Translate text to Chinese with caching"""
    try:
        # Check cache first
        cached_result = CacheService.get_cached_translation(
            request.text, request.source_language, "zh"
        )

        if cached_result:
            return TranslateResponse(
                original_text=request.text,
                translated_text=cached_result["translated_text"],
                source_language=cached_result["source_language"],
                target_language="zh"
            )

        # Perform translation
        result = await translation_service.translate(
            text=request.text,
            source_lang=request.source_language,
            target_lang="zh"
        )

        # Cache the result
        CacheService.cache_translation(
            request.text, request.source_language, "zh", result
        )

        return TranslateResponse(
            original_text=request.text,
            translated_text=result["translated_text"],
            source_language=result["source_language"],
            target_language="zh"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Translation failed: {str(e)}")

@app.post("/api/search", response_model=SearchResponse)
async def search_videos(
    request: SearchRequest,
    current_user: Optional[dict] = Depends(get_current_user_optional)
):
    """Search for Douyin videos with caching"""
    try:
        # Check cache first
        cached_result = CacheService.get_cached_search_results(
            request.query, request.limit
        )

        if cached_result:
            return SearchResponse(
                query=request.query,
                videos=[VideoInfo(**video) for video in cached_result["videos"]]
            )

        # Perform search
        videos = await douyin_service.search_videos(
            query=request.query,
            limit=request.limit
        )

        # Cache the result
        search_result = {
            "query": request.query,
            "videos": videos
        }
        CacheService.cache_search_results(request.query, request.limit, search_result)

        return SearchResponse(
            query=request.query,
            videos=[VideoInfo(**video) for video in videos]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.post("/api/download", response_model=DownloadResponse)
async def download_video(request: DownloadRequest):
    """Download a Douyin video"""
    try:
        result = await download_service.download_video(
            url=request.video_url,
            quality=request.quality
        )
        return DownloadResponse(
            success=result["success"],
            message=result["message"],
            file_path=result.get("file_path"),
            file_size=result.get("file_size")
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Download failed: {str(e)}")

@app.get("/api/download/{filename}")
async def get_downloaded_file(filename: str):
    """Serve downloaded files"""
    file_path = os.path.join("downloads", filename)
    if os.path.exists(file_path):
        return FileResponse(file_path)
    else:
        raise HTTPException(status_code=404, detail="File not found")

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "services": "operational"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
