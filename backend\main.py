from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from pydantic import BaseModel
import os
import uvicorn
from typing import List, Optional
import asyncio

# Import our services
from app.services.translation_service import TranslationService
from app.services.douyin_service import DouyinService
from app.services.download_service import DownloadService
from app.database import init_db

app = FastAPI(
    title="Douyin Translator App",
    description="Modern app for text translation and Douyin video search/download",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
translation_service = TranslationService()
douyin_service = DouyinService()
download_service = DownloadService()

# Pydantic models
class TranslateRequest(BaseModel):
    text: str
    source_language: Optional[str] = "auto"

class TranslateResponse(BaseModel):
    original_text: str
    translated_text: str
    source_language: str
    target_language: str = "zh"

class SearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 10

class VideoInfo(BaseModel):
    title: str
    url: str
    duration: Optional[str] = None
    thumbnail: Optional[str] = None
    author: Optional[str] = None

class SearchResponse(BaseModel):
    query: str
    videos: List[VideoInfo]

class DownloadRequest(BaseModel):
    video_url: str
    quality: Optional[str] = "best"

class DownloadResponse(BaseModel):
    success: bool
    message: str
    file_path: Optional[str] = None
    file_size: Optional[str] = None

@app.on_event("startup")
async def startup_event():
    """Initialize database and services on startup"""
    try:
        await init_db()
        await douyin_service.initialize()
    except Exception as e:
        print(f"Startup warning: {e}")
        # Continue anyway

@app.get("/")
async def root():
    return {"message": "Douyin Translator App API", "version": "1.0.0"}

@app.post("/api/translate", response_model=TranslateResponse)
async def translate_text(request: TranslateRequest):
    """Translate text to Chinese"""
    try:
        result = await translation_service.translate(
            text=request.text,
            source_lang=request.source_language,
            target_lang="zh"
        )
        return TranslateResponse(
            original_text=request.text,
            translated_text=result["translated_text"],
            source_language=result["source_language"],
            target_language="zh"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Translation failed: {str(e)}")

@app.post("/api/search", response_model=SearchResponse)
async def search_videos(request: SearchRequest):
    """Search for Douyin videos"""
    try:
        videos = await douyin_service.search_videos(
            query=request.query,
            limit=request.limit
        )
        return SearchResponse(
            query=request.query,
            videos=[VideoInfo(**video) for video in videos]
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.post("/api/download", response_model=DownloadResponse)
async def download_video(request: DownloadRequest):
    """Download a Douyin video"""
    try:
        result = await download_service.download_video(
            url=request.video_url,
            quality=request.quality
        )
        return DownloadResponse(
            success=result["success"],
            message=result["message"],
            file_path=result.get("file_path"),
            file_size=result.get("file_size")
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Download failed: {str(e)}")

@app.get("/api/download/{filename}")
async def get_downloaded_file(filename: str):
    """Serve downloaded files"""
    file_path = os.path.join("downloads", filename)
    if os.path.exists(file_path):
        return FileResponse(file_path)
    else:
        raise HTTPException(status_code=404, detail="File not found")

@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "services": "operational"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
