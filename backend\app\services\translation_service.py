import asyncio
import os
import httpx
from typing import Dict, Any, Optional
import logging
from app.database import save_translation

# Try importing googletrans for fallback
try:
    from googletrans import Translator
    GOOGLETRANS_AVAILABLE = True
except ImportError:
    GOOGLETRANS_AVAILABLE = False

logger = logging.getLogger(__name__)

class TranslationService:
    def __init__(self):
        self.google_api_key = os.getenv('GOOGLE_TRANSLATE_API_KEY')
        self.use_google_api = bool(self.google_api_key)
        self.client = httpx.AsyncClient(timeout=30.0)

        # Initialize googletrans as fallback
        if GOOGLETRANS_AVAILABLE:
            self.translator = Translator()
            logger.info("Google Translate (googletrans) available as fallback")
        else:
            self.translator = None
            logger.warning("googletrans not available")

        if self.use_google_api:
            logger.info("Using Google Cloud Translate API")
        elif GOOGLETRANS_AVAILABLE:
            logger.info("Using googletrans library for real translation")
        else:
            logger.info("Using mock translation service (install googletrans or set GOOGLE_TRANSLATE_API_KEY for real translation)")
        
    async def translate(self, text: str, source_lang: str = "auto", target_lang: str = "zh") -> Dict[str, Any]:
        """
        Translate text from source language to target language

        Args:
            text: Text to translate
            source_lang: Source language code (auto-detect if 'auto')
            target_lang: Target language code

        Returns:
            Dictionary containing translation results
        """
        try:
            if self.use_google_api:
                result = await self._translate_with_google_api(text, source_lang, target_lang)
            elif GOOGLETRANS_AVAILABLE and self.translator:
                result = await self._translate_with_googletrans(text, source_lang, target_lang)
            else:
                result = await self._translate_with_mock(text, source_lang, target_lang)

            # Save to database
            await save_translation(
                original=text,
                translated=result["translated_text"],
                source_lang=result["source_language"],
                target_lang=target_lang
            )

            return result

        except Exception as e:
            logger.error(f"Translation error: {str(e)}")
            # Fallback chain: Google API → googletrans → mock
            if self.use_google_api and GOOGLETRANS_AVAILABLE:
                logger.warning("Google API failed, falling back to googletrans")
                try:
                    result = await self._translate_with_googletrans(text, source_lang, target_lang)
                    return result
                except Exception as e2:
                    logger.warning(f"Googletrans also failed: {e2}, falling back to mock")
                    result = await self._translate_with_mock(text, source_lang, target_lang)
                    return result
            elif self.use_google_api:
                logger.warning("Google API failed, falling back to mock translation")
                result = await self._translate_with_mock(text, source_lang, target_lang)
                return result
            raise Exception(f"Translation failed: {str(e)}")
    
    async def _translate_with_google_api(self, text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
        """Translate using Google Cloud Translate API"""
        try:
            # Google Cloud Translate API endpoint
            url = f"https://translation.googleapis.com/language/translate/v2?key={self.google_api_key}"

            # Prepare request data
            data = {
                'q': text,
                'target': target_lang if target_lang != 'zh' else 'zh-CN',
                'format': 'text'
            }

            if source_lang != 'auto':
                data['source'] = source_lang

            # Make API request
            response = await self.client.post(url, data=data)
            response.raise_for_status()

            result = response.json()

            if 'data' in result and 'translations' in result['data']:
                translation = result['data']['translations'][0]
                detected_lang = translation.get('detectedSourceLanguage', source_lang)

                return {
                    "translated_text": translation['translatedText'],
                    "source_language": detected_lang,
                    "confidence": 0.99
                }
            else:
                raise Exception("Invalid response from Google Translate API")

        except Exception as e:
            logger.error(f"Google API translation error: {str(e)}")
            raise Exception(f"Google API translation failed: {str(e)}")

    async def _translate_with_googletrans(self, text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
        """Translate using googletrans library"""
        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                self._googletrans_sync,
                text,
                source_lang,
                target_lang
            )
            return result

        except Exception as e:
            logger.error(f"Googletrans error: {str(e)}")
            raise Exception(f"Googletrans failed: {str(e)}")

    def _googletrans_sync(self, text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
        """Synchronous googletrans translation"""
        try:
            # Convert target language
            dest_lang = 'zh-cn' if target_lang == 'zh' else target_lang

            # Detect language if auto
            if source_lang == "auto":
                detected = self.translator.detect(text)
                source_lang = detected.lang
                logger.info(f"Detected language: {source_lang} (confidence: {detected.confidence})")

            # Translate text
            translation = self.translator.translate(
                text,
                src=source_lang if source_lang != "auto" else None,
                dest=dest_lang
            )

            return {
                "translated_text": translation.text,
                "source_language": translation.src,
                "confidence": 0.95
            }

        except Exception as e:
            logger.error(f"Googletrans sync error: {str(e)}")
            raise Exception(f"Googletrans sync failed: {str(e)}")

    async def _translate_with_mock(self, text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
        """Mock translation method for development/fallback"""
        try:
            # Enhanced mock translations with more phrases
            mock_translations = {
                "hello": "你好",
                "hello world": "你好世界",
                "good morning": "早上好",
                "good afternoon": "下午好",
                "good evening": "晚上好",
                "good night": "晚安",
                "thank you": "谢谢",
                "thanks": "谢谢",
                "how are you": "你好吗",
                "how are you doing": "你最近怎么样",
                "goodbye": "再见",
                "bye": "再见",
                "yes": "是的",
                "no": "不",
                "please": "请",
                "sorry": "对不起",
                "excuse me": "不好意思",
                "welcome": "欢迎",
                "beautiful": "美丽",
                "amazing": "令人惊叹",
                "wonderful": "精彩",
                "fantastic": "太棒了",
                "love": "爱",
                "happy": "快乐",
                "sad": "悲伤",
                "music": "音乐",
                "dance": "舞蹈",
                "video": "视频",
                "funny": "有趣",
                "cute": "可爱",
                "cool": "酷",
                "awesome": "棒极了",
                "buddha": "佛陀",
                "meditation": "冥想",
                "peace": "和平",
                "wisdom": "智慧",
                "enlightenment": "觉悟"
            }

            # Simple mock translation
            text_lower = text.lower().strip()
            if text_lower in mock_translations:
                translated_text = mock_translations[text_lower]
            else:
                # More sophisticated fallback
                words = text_lower.split()
                translated_words = []
                for word in words:
                    if word in mock_translations:
                        translated_words.append(mock_translations[word])
                    else:
                        translated_words.append(f"[{word}]")

                if translated_words:
                    translated_text = " ".join(translated_words)
                else:
                    translated_text = f"[中文翻译] {text}"

            # Detect source language (mock)
            if source_lang == "auto":
                # Simple language detection based on characters
                if any('\u4e00' <= char <= '\u9fff' for char in text):
                    source_lang = "zh"
                elif any('\u3040' <= char <= '\u309f' or '\u30a0' <= char <= '\u30ff' for char in text):
                    source_lang = "ja"
                elif any('\uac00' <= char <= '\ud7af' for char in text):
                    source_lang = "ko"
                elif text.isascii():
                    source_lang = "en"
                else:
                    source_lang = "unknown"

            return {
                "translated_text": translated_text,
                "source_language": source_lang,
                "confidence": 0.85
            }

        except Exception as e:
            logger.error(f"Mock translation error: {str(e)}")
            raise Exception(f"Mock translation service error: {str(e)}")
    
    async def detect_language(self, text: str) -> Dict[str, Any]:
        """
        Detect the language of given text
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary containing language detection results
        """
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, 
                self._detect_language_sync, 
                text
            )
            return result
            
        except Exception as e:
            logger.error(f"Language detection error: {str(e)}")
            raise Exception(f"Language detection failed: {str(e)}")
    
    def _detect_language_sync(self, text: str) -> Dict[str, Any]:
        """Synchronous language detection method"""
        try:
            detected = self.translator.detect(text)
            return {
                "language": detected.lang,
                "confidence": detected.confidence
            }
        except Exception as e:
            logger.error(f"Sync language detection error: {str(e)}")
            raise Exception(f"Language detection service error: {str(e)}")
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get list of supported languages"""
        return {
            'auto': 'Auto-detect',
            'en': 'English',
            'zh': 'Chinese (Simplified)',
            'zh-tw': 'Chinese (Traditional)',
            'ja': 'Japanese',
            'ko': 'Korean',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'ar': 'Arabic',
            'hi': 'Hindi',
            'th': 'Thai',
            'vi': 'Vietnamese'
        }
