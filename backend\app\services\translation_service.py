import asyncio
from googletrans import Translator
from typing import Dict, Any
import logging
from app.database import save_translation

logger = logging.getLogger(__name__)

class TranslationService:
    def __init__(self):
        self.translator = Translator()
        
    async def translate(self, text: str, source_lang: str = "auto", target_lang: str = "zh") -> Dict[str, Any]:
        """
        Translate text from source language to target language
        
        Args:
            text: Text to translate
            source_lang: Source language code (auto-detect if 'auto')
            target_lang: Target language code
            
        Returns:
            Dictionary containing translation results
        """
        try:
            # Run translation in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, 
                self._translate_sync, 
                text, 
                source_lang, 
                target_lang
            )
            
            # Save to database
            await save_translation(
                original=text,
                translated=result["translated_text"],
                source_lang=result["source_language"],
                target_lang=target_lang
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Translation error: {str(e)}")
            raise Exception(f"Translation failed: {str(e)}")
    
    def _translate_sync(self, text: str, source_lang: str, target_lang: str) -> Dict[str, Any]:
        """Synchronous translation method"""
        try:
            # For now, use a mock translation since googletrans has issues
            # In production, you would use a proper translation API
            mock_translations = {
                "hello": "你好",
                "hello world": "你好世界",
                "good morning": "早上好",
                "thank you": "谢谢",
                "how are you": "你好吗",
                "goodbye": "再见",
                "yes": "是的",
                "no": "不",
                "please": "请",
                "sorry": "对不起"
            }

            # Simple mock translation
            text_lower = text.lower().strip()
            if text_lower in mock_translations:
                translated_text = mock_translations[text_lower]
            else:
                # Fallback: add Chinese prefix to indicate translation
                translated_text = f"[中文] {text}"

            # Detect source language (mock)
            if source_lang == "auto":
                # Simple language detection based on characters
                if any('\u4e00' <= char <= '\u9fff' for char in text):
                    source_lang = "zh"
                elif text.isascii():
                    source_lang = "en"
                else:
                    source_lang = "unknown"

            return {
                "translated_text": translated_text,
                "source_language": source_lang,
                "confidence": 0.95
            }

        except Exception as e:
            logger.error(f"Sync translation error: {str(e)}")
            raise Exception(f"Translation service error: {str(e)}")
    
    async def detect_language(self, text: str) -> Dict[str, Any]:
        """
        Detect the language of given text
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary containing language detection results
        """
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, 
                self._detect_language_sync, 
                text
            )
            return result
            
        except Exception as e:
            logger.error(f"Language detection error: {str(e)}")
            raise Exception(f"Language detection failed: {str(e)}")
    
    def _detect_language_sync(self, text: str) -> Dict[str, Any]:
        """Synchronous language detection method"""
        try:
            detected = self.translator.detect(text)
            return {
                "language": detected.lang,
                "confidence": detected.confidence
            }
        except Exception as e:
            logger.error(f"Sync language detection error: {str(e)}")
            raise Exception(f"Language detection service error: {str(e)}")
    
    def get_supported_languages(self) -> Dict[str, str]:
        """Get list of supported languages"""
        return {
            'auto': 'Auto-detect',
            'en': 'English',
            'zh': 'Chinese (Simplified)',
            'zh-tw': 'Chinese (Traditional)',
            'ja': 'Japanese',
            'ko': 'Korean',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'it': 'Italian',
            'pt': 'Portuguese',
            'ru': 'Russian',
            'ar': 'Arabic',
            'hi': 'Hindi',
            'th': 'Thai',
            'vi': 'Vietnamese'
        }
