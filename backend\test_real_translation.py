#!/usr/bin/env python3
"""
Test real translation functionality
"""

import asyncio
from googletrans import Translator

async def test_googletrans():
    """Test googletrans directly"""
    print("🔍 Testing googletrans directly...")
    
    try:
        translator = Translator()
        
        # Test simple translation
        result = translator.translate("Buddha", src='en', dest='zh-cn')
        print(f"✅ Translation successful!")
        print(f"   Original: Buddha")
        print(f"   Translated: {result.text}")
        print(f"   Source: {result.src}")
        print(f"   Destination: {result.dest}")
        
        # Test longer text
        result2 = translator.translate("Buddha teaches us about meditation and wisdom", src='en', dest='zh-cn')
        print(f"✅ Long text translation successful!")
        print(f"   Original: Buddha teaches us about meditation and wisdom")
        print(f"   Translated: {result2.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Googletrans test failed: {e}")
        return False

async def test_translation_service():
    """Test our translation service"""
    print("\n🔍 Testing our translation service...")
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app.services.translation_service import TranslationService
        
        service = TranslationService()
        result = await service.translate("Buddha", "en", "zh")
        
        print(f"✅ Translation service successful!")
        print(f"   Original: Buddha")
        print(f"   Translated: {result['translated_text']}")
        print(f"   Source: {result['source_language']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Translation service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🧪 REAL TRANSLATION TESTING")
    print("=" * 40)
    
    # Test googletrans directly
    googletrans_ok = await test_googletrans()
    
    # Test our service
    service_ok = await test_translation_service()
    
    print("\n" + "=" * 40)
    if googletrans_ok and service_ok:
        print("🎉 All translation tests passed!")
    else:
        print("⚠️ Some translation tests failed")
        if googletrans_ok:
            print("   ✅ Googletrans working")
        else:
            print("   ❌ Googletrans failed")
        if service_ok:
            print("   ✅ Translation service working")
        else:
            print("   ❌ Translation service failed")

if __name__ == "__main__":
    asyncio.run(main())
